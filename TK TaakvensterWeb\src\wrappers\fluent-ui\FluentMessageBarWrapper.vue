<template>
  <fluent-message-bar 
    :intent="intent"
    :layout="layout"
    :shape="shape"
    v-bind="$attrs"
    @dismiss="$emit('dismiss')"
    ref="messageBarRef"
  >
    <!-- Default slot for main content -->
    <slot></slot>
    
    <!-- Icon slot for intent representation -->
    <template #icon>
      <slot name="icon"></slot>
    </template>
    
    <!-- Actions slot for buttons -->
    <template #actions>
      <slot name="actions"></slot>
    </template>
    
    <!-- Dismiss slot for custom close button -->
    <template #dismiss>
      <slot name="dismiss"></slot>
    </template>
  </fluent-message-bar>
</template>

<script setup>
import { ref, onMounted, defineExpose } from 'vue';
import { MessageBarDefinition, FluentDesignSystem } from '@fluentui/web-components';

// Ensure the message bar component is registered
if (!customElements.get('fluent-message-bar')) {
  FluentDesignSystem.registry.register(MessageBarDefinition());
}

// Reference to the native message bar element
const messageBarRef = ref(null);

// Props match exactly the attributes in the Fluent UI web component API
const props = defineProps({
  intent: {
    type: String,
    default: 'info',
    validator: (value) => ['info', 'warning', 'error', 'success'].includes(value)
  },
  layout: {
    type: String,
    default: 'singleline',
    validator: (value) => ['singleline', 'multiline'].includes(value)
  },
  shape: {
    type: String,
    default: 'rounded',
    validator: (value) => ['rounded', 'square'].includes(value)
  }
});

// Define emitted events
defineEmits(['dismiss']);

// Expose the dismiss method from the native web component
const dismiss = () => {
  if (messageBarRef.value) {
    messageBarRef.value.dismiss();
  }
};

// Expose methods to match the Fluent UI web component API
defineExpose({
  dismiss
});
</script>

<style scoped>
/* No additional styling needed as we're using the native Fluent UI styling */
/* The component inherits all styles from the Fluent UI web component */
</style>
