import { defineStore } from 'pinia';
import { MESSAGE_BAR_DEFAULTS } from '../composables/useMessageBar/constants';

/**
 * Pinia store for managing message bar notifications
 */
export const useMessageBarStore = defineStore('messageBar', {
  state: () => ({
    messages: new Map(),
    counter: 0
  }),
  
  getters: {
    /**
     * Get messages as an array sorted by creation time (newest first)
     */
    messageArray: (state) => Array.from(state.messages.values())
      .sort((a, b) => b.createdAt - a.createdAt),
    
    /**
     * Get the number of active messages
     */
    activeCount: (state) => state.messages.size
  },
  
  actions: {
    /**
     * Show a message bar notification
     * @param {string} message - The message to display
     * @param {Object} options - Configuration options
     * @returns {string} The ID of the created message bar
     */
    notify(message, options = {}) {
      // Input validation
      if (!message || typeof message !== 'string' || message.trim() === '') {
        throw new Error('Message must be a non-empty string');
      }
      
      if (typeof options !== 'object' || options === null) {
        throw new Error('Options must be an object');
      }
      
      if (options.intent && !MESSAGE_BAR_DEFAULTS.intents.includes(options.intent)) {
        throw new Error(`Invalid intent: ${options.intent}. Must be one of: ${MESSAGE_BAR_DEFAULTS.intents.join(', ')}`);
      }
      
      if (options.duration !== undefined && (typeof options.duration !== 'number' || options.duration < 0)) {
        throw new Error('Duration must be a non-negative number');
      }
      
      if (options.actions && !Array.isArray(options.actions)) {
        throw new Error('Actions must be an array');
      }
      
      if (options.persistent !== undefined && typeof options.persistent !== 'boolean') {
        throw new Error('Persistent must be a boolean');
      }
      this.counter++;
      const id = `message-${Date.now()}-${this.counter}`;
      
      const messageData = {
        id,
        message,
        intent: options.intent || 'info',
        actions: options.actions || [],
        duration: options.duration || MESSAGE_BAR_DEFAULTS.duration,
        persistent: options.persistent || false,
        createdAt: Date.now()
      };
      
      this.messages.set(id, messageData);
      
      // Auto-dismiss logic
      if (messageData.duration > 0 && !messageData.persistent) {
        setTimeout(() => this.dismiss(id), messageData.duration);
      }
      
      // Enforce maximum visible MessageBars (FIFO)
      // Use timestamp-based ordering to reliably get the oldest message
      if (this.messages.size > MESSAGE_BAR_DEFAULTS.maxVisible) {
        const oldest = this.messageArray[this.messageArray.length - 1]; // Last in sorted array = oldest
        this.dismiss(oldest.id);
      }
      
      return id;
    },
    
    /**
     * Dismiss a message bar by ID
     * @param {string} id - The ID of the message bar to dismiss
     */
    dismiss(id) {
      // Input validation
      if (!id || typeof id !== 'string') {
        throw new Error('ID must be a non-empty string');
      }
      
      this.messages.delete(id);
    },
    
    /**
     * Handle action button click
     * @param {Object} actionData - Action data with actionId and messageId
     */
    handleAction(actionData) {
      // Input validation
      if (!actionData || typeof actionData !== 'object') {
        throw new Error('ActionData must be an object');
      }
      
      const { actionId, messageId } = actionData;
      
      if (!actionId || typeof actionId !== 'string') {
        throw new Error('ActionId must be a non-empty string');
      }
      
      if (!messageId || typeof messageId !== 'string') {
        throw new Error('MessageId must be a non-empty string');
      }
      const message = this.messages.get(messageId);
      
      if (message) {
        const action = message.actions.find(a => a.id === actionId);
        if (action && action.onClick) {
          action.onClick();
        }
        
        // Auto-dismiss after action unless persistent
        if (!message.persistent) {
          this.dismiss(messageId);
        }
      }
    },
    
    /**
     * Clear all message bars
     */
    clear() {
      this.messages.clear();
    },
    
    /**
     * Show a success message
     * @param {string} message - The message to display
     * @param {Object} options - Configuration options
     * @returns {string} The ID of the created message bar
     */
    success(message, options = {}) {
      return this.notify(message, { ...options, intent: 'success' });
    },
    
    /**
     * Show an error message
     * @param {string} message - The message to display
     * @param {Object} options - Configuration options
     * @returns {string} The ID of the created message bar
     */
    error(message, options = {}) {
      return this.notify(message, { ...options, intent: 'error' });
    },
    
    /**
     * Show a warning message
     * @param {string} message - The message to display
     * @param {Object} options - Configuration options
     * @returns {string} The ID of the created message bar
     */
    warning(message, options = {}) {
      return this.notify(message, { ...options, intent: 'warning' });
    },
    
    /**
     * Show an info message
     * @param {string} message - The message to display
     * @param {Object} options - Configuration options
     * @returns {string} The ID of the created message bar
     */
    info(message, options = {}) {
      return this.notify(message, { ...options, intent: 'info' });
    }
  }
});
