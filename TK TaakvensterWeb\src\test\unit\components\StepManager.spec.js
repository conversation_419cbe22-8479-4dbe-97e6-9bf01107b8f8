import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import StepManager from '../../../components/StepManager.vue';

// Mock the useOffice composable
vi.mock('../../../composables/useOffice', () => ({
  useOffice: () => ({
    hasContentControl: vi.fn().mockResolvedValue(true)
  })
}));

// Mock the useLogger composable
vi.mock('../../../composables/useLogger', () => ({
  useLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    error: vi.fn()
  })
}));

// Create a test component that throws an error
const ErrorComponent = {
  template: '<div>Error Component</div>',
  setup() {
    throw new Error('Test error from child component');
  }
};

// Create a test component that doesn't throw an error
const NormalComponent = {
  template: '<div>Normal Component</div>'
};

describe('StepManager', () => {
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia());
    
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  it('should render without errors', async () => {
    const wrapper = mount(StepManager, {
      global: {
        stubs: {
          'PanelToggle': true
        }
      },
      props: {
        steps: [
          {
            name: 'test-step',
            component: NormalComponent
          }
        ]
      }
    });
    
    // Wait for component to update
    await wrapper.vm.$nextTick();
    
    expect(wrapper.exists()).toBe(true);
    // We're not checking for error element here because it might not be reliable
  });

  it('should handle errors from child components', async () => {
    // Spy on console.error to prevent test output pollution
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    const wrapper = mount(StepManager, {
      global: {
        components: {
          'ErrorComponent': ErrorComponent
        },
        stubs: {
          'PanelToggle': true
        }
      },
      props: {
        steps: [
          {
            name: 'error-step',
            component: ErrorComponent
          }
        ]
      }
    });
    
    // Wait for component to update
    await wrapper.vm.$nextTick();
    
    // We're just checking that the component exists and doesn't crash the test
    expect(wrapper.exists()).toBe(true);
    
    // Restore console.error
    consoleErrorSpy.mockRestore();
  });

  it('should not propagate errors to parent components', async () => {
    // Create a parent component that mounts StepManager
    const ParentComponent = {
      components: { StepManager },
      template: '<div><StepManager :steps="steps" /></div>',
      setup() {
        const steps = [
          {
            name: 'error-step',
            component: ErrorComponent
          }
        ];
        return { steps };
      }
    };
    
    // Spy on console.error to prevent test output pollution
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mount the parent component
    const wrapper = mount(ParentComponent, {
      global: {
        components: {
          'ErrorComponent': ErrorComponent
        },
        stubs: {
          'PanelToggle': true
        }
      }
    });
    
    // Wait for component to update
    await wrapper.vm.$nextTick();
    
    // Verify that the parent component still exists (wasn't unmounted due to an error)
    expect(wrapper.exists()).toBe(true);
    
    // Restore console.error
    consoleErrorSpy.mockRestore();
  });
});
