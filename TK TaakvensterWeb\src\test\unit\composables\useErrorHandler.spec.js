import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createP<PERSON>, setActivePinia } from 'pinia';

// Mock the messageBarStore - must be before importing useErrorHandler
vi.mock('../../../stores/messageBarStore', () => {
  const mockStore = {
    notify: vi.fn(),
    dismiss: vi.fn(),
    clear: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
    handleAction: vi.fn()
  };
  return {
    useMessageBarStore: vi.fn(() => mockStore)
  };
});

// Import the mocked store to verify calls
import { useMessageBarStore } from '../../../stores/messageBarStore';

// Import useErrorHandler after the mock is set up
import { useErrorHandler } from '../../../composables/useErrorHandler';

describe('useErrorHandler', () => {
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia());
    
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  describe('handleError', () => {
    it('should log the error and add an error message to the message bar', () => {
      const { handleError } = useErrorHandler();
      const error = new Error('Test error');
      const context = 'test context';
      const options = { userMessage: 'User-friendly error message' };
      
      // Spy on console.error
      const consoleErrorSpy = vi.spyOn(console, 'error');
      
      handleError(error, context, options);
      
      // Verify console.error was called with the right arguments
      expect(consoleErrorSpy).toHaveBeenCalledWith(`Error ${context}:`, error);
      
      // Verify the message bar store was called with the right arguments
      const messageBarStore = useMessageBarStore();
      expect(messageBarStore.error).toHaveBeenCalledWith(options.userMessage);
    });

    it('should use the error message if no userMessage is provided', () => {
      const { handleError } = useErrorHandler();
      const error = new Error('Test error');
      const context = 'test context';
      
      handleError(error, context);
      
      // Verify the message bar store was called with the error message
      const messageBarStore = useMessageBarStore();
      expect(messageBarStore.error).toHaveBeenCalledWith('Operation failed: Test error');
    });

    it('should not add a message to the message bar if silent is true', () => {
      const { handleError } = useErrorHandler();
      const error = new Error('Test error');
      const context = 'test context';
      const options = { silent: true };
      
      handleError(error, context, options);
      
      // Verify the message bar store was not called
      const messageBarStore = useMessageBarStore();
      expect(messageBarStore.error).not.toHaveBeenCalled();
    });
  });

  describe('withErrorHandling', () => {
    it('should execute the callback and return its result on success', async () => {
      const { withErrorHandling } = useErrorHandler();
      const callback = vi.fn().mockResolvedValue('success');
      const context = 'test context';
      const options = {};
      
      const result = await withErrorHandling(callback, context, options);
      
      // Verify the callback was called
      expect(callback).toHaveBeenCalled();
      
      // Verify the result is correct
      expect(result).toBe('success');
      
      // Error handler should never show success messages
      const messageBarStore = useMessageBarStore();
      expect(messageBarStore.success).not.toHaveBeenCalled();
    });

    it('should handle errors and call handleError', async () => {
      const { withErrorHandling } = useErrorHandler();
      const error = new Error('Test error');
      const callback = vi.fn().mockRejectedValue(error);
      const context = 'test context';
      const options = { userMessage: 'User-friendly error message' };
      
      // Spy on console.error
      const consoleErrorSpy = vi.spyOn(console, 'error');
      
      await expect(withErrorHandling(callback, context, options)).rejects.toThrow(error);
      
      // Verify console.error was called with the right arguments
      expect(consoleErrorSpy).toHaveBeenCalledWith(`Error ${context}:`, error);
      
      // Verify the message bar store was called with the right arguments
      const messageBarStore = useMessageBarStore();
      expect(messageBarStore.error).toHaveBeenCalledWith(options.userMessage);
    });

    it('should not rethrow the error if rethrow is false', async () => {
      const { withErrorHandling } = useErrorHandler();
      const error = new Error('Test error');
      const callback = vi.fn().mockRejectedValue(error);
      const context = 'test context';
      const options = { userMessage: 'User-friendly error message', rethrow: false };
      
      await expect(withErrorHandling(callback, context, options)).resolves.toBeUndefined();
    });

    it('should not show a success message if successMessage is not provided', async () => {
      const { withErrorHandling } = useErrorHandler();
      const callback = vi.fn().mockResolvedValue('success');
      const context = 'test context';
      
      await withErrorHandling(callback, context);
      
      // Verify the message bar store was not called
      const messageBarStore = useMessageBarStore();
      expect(messageBarStore.success).not.toHaveBeenCalled();
    });
  });
});
