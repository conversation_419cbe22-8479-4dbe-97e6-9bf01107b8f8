import { ref, watch, onUnmounted } from 'vue';
import { onKeyStroke } from '@vueuse/core';
import { useLogger } from './useLogger';

/**
 * Composable for all panel keyboard shortcuts
 * Handles global 'd' key, Escape key, and number keys (1-9) for step navigation
 * 
 * @param {Object} options - Configuration options
 * @param {Ref<boolean>} options.isVisible - Panel visibility state
 * @param {Ref<number>} options.stepCount - Number of available steps
 * @param {Function} options.onToggle - Callback to toggle panel visibility
 * @param {Function} options.onSelectStep - Callback when step is selected
 * @returns {Object} - Composable interface
 */
export function usePanelKeyboardShortcuts(options) {
  const { isVisible, stepCount, onToggle, onSelectStep } = options;
  const { debug } = useLogger();
  
  // Store active keyboard listeners for cleanup
  const activeListeners = ref(new Set());
  
  /**
   * Check if keyboard event should be handled for local shortcuts
   */
  function shouldHandleLocalEvent(event) {
    if (!isVisible.value) return false;
    
    const target = event.target;
    const tagName = target.tagName.toLowerCase();
    
    // Skip if user is typing in form fields
    return !(['input', 'textarea', 'select'].includes(tagName) || 
             target.contentEditable === 'true');
  }
  
  /**
   * Check if keyboard event should be handled for global shortcuts
   */
  function shouldHandleGlobalEvent(event) {
    const target = event.target;
    const tagName = target.tagName.toLowerCase();
    
    // Skip if user is typing in form fields (global shortcuts should be less intrusive)
    return !(['input', 'textarea', 'select'].includes(tagName) || 
             target.contentEditable === 'true');
  }
  
  /**
   * Setup global 'd' key listener (works anywhere)
   */
  function setupGlobalToggle() {
    const stopListener = onKeyStroke('d', (event) => {
        if (!shouldHandleGlobalEvent(event) || !event.ctrlKey) return;
        event.preventDefault();
        onToggle();
        debug('Panel toggled via "Ctrl + D" key');
    });
    
    activeListeners.value.add(stopListener);
    return stopListener;
  }
  
  /**
   * Setup Escape key listener (only when panel visible)
   */
  function setupEscapeKey() {
    const stopListener = onKeyStroke('Escape', (event) => {
      if (!shouldHandleLocalEvent(event)) return;
      
      event.preventDefault();
      // Close panel by toggling when visible
      if (isVisible.value) {
        onToggle();
        debug('Panel closed via Escape key');
      }
    });
    
    activeListeners.value.add(stopListener);
    return stopListener;
  }
  
  /**
   * Setup number key listeners (1-9) - only when panel visible
   */
  function setupNumberKeys(maxSteps) {
    const listeners = [];
    
    for (let keyNumber = 1; keyNumber <= Math.min(9, maxSteps); keyNumber++) {
      const stopListener = onKeyStroke(keyNumber.toString(), (event) => {
        if (!shouldHandleLocalEvent(event)) return;
        
        event.preventDefault();
        const stepIndex = keyNumber - 1;
        onSelectStep(stepIndex);
        debug(`Step ${keyNumber} selected via keyboard`);
      });
      
      activeListeners.value.add(stopListener);
      listeners.push(stopListener);
    }
    
    return listeners;
  }
  
  /**
   * Clear all active keyboard listeners
   */
  function clearAllListeners() {
    activeListeners.value.forEach(stopListener => stopListener());
    activeListeners.value.clear();
  }
  
  /**
   * Initialize all keyboard shortcuts
   */
  function initializeShortcuts() {
    clearAllListeners();
    
    // Global shortcuts (always active)
    setupGlobalToggle();
    
    // Local shortcuts (context-aware)
    setupEscapeKey();
    setupNumberKeys(stepCount.value);
    
    debug(`Panel shortcuts initialized: global toggle + ${stepCount.value} local shortcuts`);
  }
  
  // Initialize shortcuts and watch for step count changes
  initializeShortcuts();
  watch(stepCount, initializeShortcuts);
  
  // Cleanup on component unmount
  onUnmounted(clearAllListeners);
  
  // No return needed - composable handles everything automatically
}
