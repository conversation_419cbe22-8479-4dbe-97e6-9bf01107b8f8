<template>
  <div class="message-bar-test p-4 bg-white rounded-lg shadow-lg border">
    <h2 class="text-lg font-semibold mb-4">MessageBar Test Panel</h2>
    
    <!-- Basic MessageBar Tests -->
    <div class="mb-4">
      <h3 class="text-sm font-medium mb-2">Basic MessageBars</h3>
      <div class="grid grid-cols-2 gap-2">
        <button @click="showSuccess" class="px-3 py-1 text-sm rounded font-medium bg-green-100 text-green-700 hover:bg-green-200">Success</button>
        <button @click="showError" class="px-3 py-1 text-sm rounded font-medium bg-red-100 text-red-700 hover:bg-red-200">Error</button>
        <button @click="showInfo" class="px-3 py-1 text-sm rounded font-medium bg-blue-100 text-blue-700 hover:bg-blue-200">Info</button>
        <button @click="showWarning" class="px-3 py-1 text-sm rounded font-medium bg-orange-100 text-orange-700 hover:bg-orange-200">Warning.</button>
      </div>
    </div>
    
    <!-- Advanced Options -->
    <div class="mb-4">
      <h3 class="text-sm font-medium mb-2">Advanced Options</h3>
      <div class="grid grid-cols-2 gap-2">
        <button @click="showWithActions" class="px-3 py-1 text-sm rounded font-medium bg-purple-100 text-purple-700 hover:bg-purple-200">With Actions</button>
        <button @click="showPersistent" class="px-3 py-1 text-sm rounded font-medium bg-indigo-100 text-indigo-700 hover:bg-indigo-200">Persistent</button>
        <button @click="showMultiline" class="px-3 py-1 text-sm rounded font-medium bg-pink-100 text-pink-700 hover:bg-pink-200">Multiline</button>
        <button @click="showCustomDuration" class="px-3 py-1 text-sm rounded font-medium bg-teal-100 text-teal-700 hover:bg-teal-200">Custom Duration</button>
      </div>
    </div>
    
    <!-- Controls -->
    <div class="flex space-x-2">
      <button @click="clearAll" class="px-3 py-1 text-sm rounded font-medium bg-gray-100 text-gray-700 hover:bg-gray-200">Clear All</button>
      <div class="text-xs ml-4 pt-1">
        <div>Active Count: {{ activeCount }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useMessageBarStore } from '../stores/messageBarStore';

// Use the Pinia store instead of inject
const messageBarStore = useMessageBarStore();
console.log('MessageBarTest: Using messageBarStore from Pinia');

const activeCount = computed(() => {
  console.log('MessageBarTest: Computing activeCount, messages count:', messageBarStore.activeCount);
  return messageBarStore.activeCount;
});

// Basic message bars
const showSuccess = () => messageBarStore.success('Operation completed successfully!');
const showError = () => messageBarStore.error('An error occurred while processing your request.');
const showInfo = () => messageBarStore.info('New updates are available.');
const showWarning = () => messageBarStore.warning('This action cannot be undone. This warning has a very long text to demonstrate that the text will be cut off instead of using a multiline approach');

// Advanced options
const showWithActions = () => {
  messageBarStore.info('Would you like to save your changes?', {
    actions: [
      { label: 'Save', id: 'save', onClick: () => messageBarStore.success('Changes saved!') },
      { label: 'Discard', id: 'discard', onClick: () => messageBarStore.info('Changes discarded.') }
    ]
  });
};

const showPersistent = () => {
  messageBarStore.warning('This message will not auto-dismiss.', { persistent: true });
};

const showMultiline = () => {
  messageBarStore.info('This is a multiline message with more detailed information. It can span multiple lines and provide more context to the user.', {
    layout: 'multiline'
  });
};

const showCustomDuration = () => {
  messageBarStore.success('This message will disappear after 10 seconds.', {
    duration: 10000
  });
};

const clearAll = () => messageBarStore.clear();
</script>

<style scoped>
.message-bar-test {
  max-width: 500px;
  margin: 20px auto;
}
</style>
