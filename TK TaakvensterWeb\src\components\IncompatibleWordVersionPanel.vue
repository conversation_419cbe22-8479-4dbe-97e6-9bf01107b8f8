<template>
  <div class="incompatible-panel flex flex-col items-center justify-center p-6">
    <div class="text-center max-w-md mx-auto">
      <h2 class="text-xl font-semibold mb-4">Incompatible Word Version</h2>
      <p class="mb-4">{{ message }}</p>
      <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
        <p class="text-sm">
          This add-in requires Word 2016 or later.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  message: {
    type: String,
    default: "This add-in requires Word 2016 or later. Some features may not be available in your version of Office."
  }
});
</script>

<style scoped>
.incompatible-panel {
  background-color: #f9f9f9;
  border-top: 4px solid #d13438;
  min-height: 100%;
  flex-grow: 1;
  width: 100%;
}
</style>
