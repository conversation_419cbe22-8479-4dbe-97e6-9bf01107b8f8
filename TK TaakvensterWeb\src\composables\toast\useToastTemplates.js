/**
 * Simplified Toast Templates
 * Simple template objects instead of complex DOM extraction
 */
import { ref } from 'vue'

export function useToastTemplates() {
  // Fluent UI styled templates
  const templates = {
    success: {
      icon: '✓',
      bgClass: 'fluent-toast-success',
      iconClass: 'fluent-icon-success'
    },
    error: {
      icon: '✕', 
      bgClass: 'fluent-toast-error',
      iconClass: 'fluent-icon-error'
    },
    info: {
      icon: 'ℹ',
      bgClass: 'fluent-toast-info', 
      iconClass: 'fluent-icon-info'
    },
    warning: {
      icon: '⚠',
      bgClass: 'fluent-toast-warning',
      iconClass: 'fluent-icon-warning'
    }
  }

  const isLoaded = ref(true) // Always loaded since templates are static

  /**
   * Compiles a template with message and ID
   * @param {string} type - Template type
   * @param {string} message - Toast message  
   * @param {string} toastId - Unique toast ID
   * @returns {string|null} Compiled HTML
   */
  const compileTemplate = (type, message, toastId) => {
    const template = templates[type]
    if (!template) {
      console.error(`Template not found for type: ${type}`)
      return null
    }

    const escapedMessage = escapeHtml(message)
    const escapedId = escapeHtml(toastId)

    return `
      <div class="fluent-toast ${template.bgClass}" role="alert">
        <div class="fluent-toast-content">
          <span class="fluent-toast-icon ${template.iconClass}">
            ${template.icon}
          </span>
          <div class="fluent-toast-message">
            ${escapedMessage}
          </div>
        </div>
        <button type="button" 
                class="fluent-toast-dismiss"
                data-toast-close="${escapedId}"
                aria-label="Close notification">
          <span class="sr-only">Close</span>
          <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
            <path d="M6.707 6l3.647-3.646a.5.5 0 0 0-.708-.708L6 5.293 2.354 1.646a.5.5 0 0 0-.708.708L5.293 6 1.646 9.646a.5.5 0 0 0 .708.708L6 6.707l3.646 3.647a.5.5 0 0 0 .708-.708L6.707 6z"/>
          </svg>
        </button>
      </div>
    `.trim()
  }

  /**
   * Escapes HTML to prevent XSS
   * @param {string} text - Text to escape
   * @returns {string} Escaped text
   */
  const escapeHtml = (text) => {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * Checks if a template exists for a given type
   * @param {string} type - Template type to check
   * @returns {boolean} True if template exists
   */
  const hasTemplate = (type) => {
    return templates.hasOwnProperty(type)
  }

  /**
   * Gets all available template types
   * @returns {string[]} Array of available template types
   */
  const getAvailableTypes = () => {
    return Object.keys(templates)
  }

  return {
    // State
    isLoaded,
    
    // Methods
    compileTemplate,
    hasTemplate,
    getAvailableTypes,
    
    // Utilities
    escapeHtml
  }
}
