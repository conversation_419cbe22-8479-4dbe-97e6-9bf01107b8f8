import { ref, reactive, readonly, computed, provide } from 'vue';
import { MESSAGE_BAR_DEFAULTS } from './constants';

/**
 * @deprecated This composable is deprecated. Use `useMessageBarStore()` from Pinia instead.
 * 
 * MIGRATION GUIDE:
 * - Replace `useMessageBar()` with `useMessageBarStore()` from '../stores/messageBarStore'
 * - The Pinia store provides the same API with better state management
 * - This composable will be removed in a future version
 * 
 * Composable for managing message bar notifications
 * @returns {Object} Methods and state for message bar management
 */
export function useMessageBar() {
  console.warn('⚠️  useMessageBar() is deprecated. Please use useMessageBarStore() from Pinia instead.');
  // State management
  const messages = reactive(new Map());
  const counter = ref(0);
  
  /**
   * Show a message bar notification
   * @param {string} message - The message to display
   * @param {Object} options - Configuration options
   * @returns {string} The ID of the created message bar
   */
  function notify(message, options = {}) {
    counter.value++;
    const id = `message-${Date.now()}-${counter.value}`;
    
    const messageData = {
      id,
      message,
      intent: options.intent || 'info',
      actions: options.actions || [],
      duration: options.duration || MESSAGE_BAR_DEFAULTS.duration,
      persistent: options.persistent || false,
      createdAt: Date.now()
    };
    
    messages.set(id, messageData);
    
    // Auto-dismiss logic
    if (messageData.duration > 0 && !messageData.persistent) {
      setTimeout(() => dismiss(id), messageData.duration);
    }
    
    // Enforce maximum visible MessageBars (FIFO)
    if (messages.size > MESSAGE_BAR_DEFAULTS.maxVisible) {
      const oldestId = Array.from(messages.keys())[0];
      dismiss(oldestId);
    }
    
    return id;
  }
  
  /**
   * Dismiss a message bar by ID
   * @param {string} id - The ID of the message bar to dismiss
   */
  function dismiss(id) {
    const message = messages.get(id);
    if (message) {
      messages.delete(id);
      if (message.onDismiss) message.onDismiss(id);
    }
  }
  
  /**
   * Clear all message bars
   */
  function clear() {
    messages.clear();
  }
  
  /**
   * Handle action button click
   * @param {Object} actionData - Action data with actionId and messageId
   */
  function handleAction(actionData) {
    const { actionId, messageId } = actionData;
    const message = messages.get(messageId);
    
    if (message) {
      const action = message.actions.find(a => a.id === actionId);
      if (action && action.onClick) {
        action.onClick();
      }
      
      // Auto-dismiss after action unless persistent
      if (!message.persistent) {
        dismiss(messageId);
      }
    }
  }
  
  // Convenience methods
  const success = (message, options = {}) => notify(message, { ...options, intent: 'success' });
  const error = (message, options = {}) => notify(message, { ...options, intent: 'error' });
  const warning = (message, options = {}) => notify(message, { ...options, intent: 'warning' });
  const info = (message, options = {}) => notify(message, { ...options, intent: 'info' });
  
  // Provide values for components
  provide('messageBarMessages', readonly(messages));
  provide('messageBarDismiss', dismiss);
  provide('messageBarAction', handleAction);
  
  return {
    // Core methods
    notify,
    dismiss,
    clear,
    handleAction,
    
    // Convenience methods
    success,
    error,
    warning,
    info,
    
    // Expose readonly state for components
    messages: readonly(messages),
    activeCount: computed(() => messages.size)
  };
}
