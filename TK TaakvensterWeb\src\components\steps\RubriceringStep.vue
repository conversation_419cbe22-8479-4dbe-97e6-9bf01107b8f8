<template>
  <div id="panel-1">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    <div class="rubricering-container">
      <FlowbiteList
        :items="buttonData"
        v-model="selectedRubricering"
        @item-click="handleRubriceringClick"
      >
        <template #item="{ item }">
          {{ item.label }}
        </template>
      </FlowbiteList>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useOffice } from '../../composables/useOffice'
import { useErrorHandler } from '../../composables/useErrorHandler'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../common/FlowbiteList.vue'
// Self-contained step information (no props needed)

const stepTitle = 'Rubricering'
const stepDescription =
  'Rubriceer het actieve document. De rubricering verschijnt op de eerste pagina in de koptekst.'

// New minimal event system
const emit = defineEmits({
  'step-ready': null, // Component loaded and ready
  'step-complete': null, // User completed this step
  'step-previous': null, // User wants to go back
})

const { insertTextInContentControl } = useOffice()
const { withErrorHandling } = useErrorHandler()
const selectedRubricering = ref(null)

const buttonData = [
  { label: 'Extern Deelbaar', value: 'TK-V Extern Deelbaar', controlTag: 'txtRubricering' },
  { label: 'Intern Deelbaar', value: 'TK-V Intern Deelbaar', controlTag: 'txtRubricering' },
  { label: 'Vertrouwelijk', value: 'TK-VERTROUWELIJK', controlTag: 'txtRubricering' },
  { label: 'Geheim', value: 'TK-GEHEIM', controlTag: 'txtRubricering' },
]

function handleRubriceringClick(event) {
  // selectedRubricering is already updated via v-model binding
  // so we just need to process the document insertion
  applyRubricering(event.index)
}

async function applyRubricering(index) {
  try {
    // Validate that a valid index was provided
    if (index === null || index === undefined || index < 0 || index >= buttonData.length) {
      await withErrorHandling(
        () => Promise.reject(new Error('Invalid selection')),
        'validating selection',
        { userMessage: 'Selecteer eerst een rubricering voordat u verder gaat.' },
      )
      return
    }

    selectedRubricering.value = index
    const button = buttonData[index]

    await withErrorHandling(
      () => insertTextInContentControl(button.controlTag, button.value),
      `applying ${button.label}`,
      {
        userMessage: `Failed to apply ${button.label}. Please try again.`,
        successMessage: `${button.label} is toegepast.`,
      },
    )

    // Signal step completion (like VBA CommandButton_Click)
    setTimeout(() => {
      emit('step-complete')
    }, 500) // Delay to ensure notification is visible before navigation
  } catch (error) {
    console.error('Error applying rubricering:', error)
    // Reset selection on error
    selectedRubricering.value = null
  }
}

// Signal that component is ready (like VBA UserForm_Initialize)
onMounted(() => {
  emit('step-ready')
})
</script>
<style scoped>
.rubricering-container {
  margin-top: 1rem !important;
  width: 100%;
}
</style>
