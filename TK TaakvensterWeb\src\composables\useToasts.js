/**
 * Composable for managing custom toasts
 * Used for bottom-right non-critical notifications
 * 
 * Features:
 * - 4 core toast types: success, error, info, warning
 * - Configurable duration and persistence
 * - Max toast limit with FIFO removal
 * - Close button support
 * - Vue 3 reactive state
 */
import { ref, reactive, computed } from 'vue';
import { useToastTemplates } from './toast/useToastTemplates.js';
import { notificationSettings } from './notificationConfig.js';

export function useToasts() {
  // Use the toast templates for HTML generation
  const toastTemplates = useToastTemplates();
  
  // Toast state management
  const activeToasts = reactive(new Map());
  const toastCounter = ref(0);
  
  // Configuration with defaults from notificationConfig
  const config = reactive({
    maxToasts: notificationSettings.toasts.maxVisible,
    duration: notificationSettings.toasts.defaultDuration
  });
  
  // Computed properties for state access
  const activeCount = computed(() => activeToasts.size);
  const isReady = computed(() => toastTemplates.isLoaded.value);
  const canShowMore = computed(() => activeToasts.size < config.maxToasts);
  
  /**
   * Generates a unique toast ID
   * @returns {string} Unique toast identifier
   */
  const generateToastId = () => {
    toastCounter.value++;
    return `toast-${Date.now()}-${toastCounter.value}`;
  };

  /**
   * Core method to show a toast
   * @param {string} type - Toast type (success, error, info, warning)
   * @param {string} message - Toast message
   * @param {Object} options - Toast options
   * @returns {string|null} Toast ID if successful
   */
  const showToast = (type, message, options = {}) => {
    if (!toastTemplates.hasTemplate(type)) {
      console.error(`No template for toast type: ${type}`);
      return null;
    }
    
    // Generate unique ID
    const toastId = generateToastId();
    
    // Get HTML content from template
    const htmlContent = toastTemplates.compileTemplate(type, message, toastId);
    if (!htmlContent) {
      console.error(`Failed to compile template for toast type: ${type}`);
      return null;
    }
    
    // Create toast object
    const toast = {
      id: toastId,
      type,
      message,
      html: htmlContent,
      duration: options.duration || config.duration,
      persistent: options.persistent || false,
      createdAt: Date.now()
    };
    
    // Add to active toasts
    activeToasts.set(toastId, toast);
    
    // Auto-remove if not persistent
    if (!toast.persistent && toast.duration > 0) {
      setTimeout(() => {
        removeToast(toastId);
      }, toast.duration);
    }
    
    // If we're over the limit, remove oldest
    if (activeToasts.size > config.maxToasts) {
      const oldestToast = Array.from(activeToasts.values())
        .sort((a, b) => a.createdAt - b.createdAt)[0];
      if (oldestToast) {
        removeToast(oldestToast.id);
      }
    }
    
    console.log(`Showing toast [${type}]: ${message} (ID: ${toastId})`);
    return toastId;
  };
  
  /**
   * Convenience methods for different toast types
   */
  const success = (message, options = {}) => showToast('success', message, options);
  const error = (message, options = {}) => showToast('error', message, options);
  const info = (message, options = {}) => showToast('info', message, options);
  const warning = (message, options = {}) => showToast('warning', message, options);

  /**
   * Remove a specific toast by ID
   * @param {string} toastId - ID of toast to remove
   */
  const removeToast = (toastId) => {
    if (activeToasts.has(toastId)) {
      activeToasts.delete(toastId);
      console.log(`Removed toast ID: ${toastId}`);
    }
  };
  
  /**
   * Clear all active toasts
   */
  const clearAllToasts = () => {
    activeToasts.clear();
    console.log('Cleared all toasts');
  };
  
  /**
   * Update toast configuration
   * @param {Object} newConfig - New configuration options
   */
  const updateConfig = (newConfig) => {
    Object.assign(config, newConfig);
    console.log('Updated toast config', config);
  };

  return {
    // Toast methods (bottom-right, non-blocking)
    success,
    error,
    info,
    warning,
    show: showToast,
    remove: removeToast,
    clear: clearAllToasts,
    
    // State access
    activeToasts,
    activeCount,
    isReady,
    canShowMore,
    
    // Configuration
    updateConfig
  };
}
