<template>
  <div 
    class="message-bar-list px-4"
    role="region"
    aria-label="Notifications"
  >
    <TransitionGroup name="message-bar-list">
      <MessageBar
        v-for="message in messageStore.messageArray"
        :key="message.id"
        v-bind="message"
        @dismiss="handleDismiss"
        @action="handleAction"
      />
    </TransitionGroup>

  </div>
</template>

<script>
import { computed, ref } from 'vue';
import { useMessageBarStore } from '../../../stores/messageBarStore';
import MessageBar from './MessageBar.vue';

export default {
  name: 'MessageBarList',
  components: {
    MessageBar
  },
  setup() {
    // Use the Pinia store
    const messageStore = useMessageBarStore();
    
    // Handle dismiss and action events
    const handleDismiss = (id) => messageStore.dismiss(id);
    const handleAction = (actionData) => messageStore.handleAction(actionData);

    return {
      messageStore,
      handleDismiss,
      handleAction
    };
  }
};
</script>

<style scoped>
.message-bar-list {
  all: revert; /* revert any styles applied by tailwind's reset */
  position: sticky;
  top: 4px;
  z-index: 1000;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin: 0;
}

/* Animations */
.message-bar-list-move, 
.message-bar-list-enter-active,
.message-bar-list-leave-active {
  transition: all 0.3s ease;
}

.message-bar-list-enter-from,
.message-bar-list-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

.message-bar-list-leave-active {
  position: absolute;
}
</style>
