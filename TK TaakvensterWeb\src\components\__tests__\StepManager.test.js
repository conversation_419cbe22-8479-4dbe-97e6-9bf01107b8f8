import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { h } from 'vue'
import StepManager from '../StepManager.vue'

// Create mock functions before mocking modules
const hasContentControlMock = vi.fn().mockResolvedValue(true)
const loggerErrorMock = vi.fn()

// Mock dependencies
vi.mock('../../composables/useOffice', () => ({
  useOffice: () => ({
    hasContentControl: hasContentControlMock,
  }),
}))

vi.mock('../../composables/useLogger', () => ({
  useLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    error: loggerErrorMock,
  }),
}))

// Mock FallbackStep component
vi.mock('../steps/FallbackStep.vue', () => ({
  default: {
    name: 'FallbackStep',
    render() {
      return h('div', { class: 'fallback-step' }, 'Fallback Step')
    },
  },
}))

// Mock PanelToggle component
vi.mock('../PanelToggle.vue', () => ({
  default: {
    name: 'PanelToggle',
    props: ['currentStep', 'steps'],
    render() {
      return h('div', { class: 'panel-toggle' }, 'Panel Toggle')
    },
  },
}))

describe('StepManager Error Handling', () => {
  // Define test components
  const TestStep = {
    name: 'TestStep',
    render() {
      return h('div', { class: 'test-step' }, 'Test Step')
    },
    emits: ['step-ready', 'step-complete', 'step-previous'],
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    // Reset mocks to default behavior
    hasContentControlMock.mockReset().mockResolvedValue(true)

    // Silence console errors during tests
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  it('should filter out steps with missing content controls', async () => {
    // Configure mock to filter out specific content control
    hasContentControlMock.mockImplementation(async (name) => {
      return name !== 'missing-control'
    })

    const steps = [
      { name: 'Valid Step', contentControl: 'valid-control', component: TestStep },
      { name: 'Missing Step', contentControl: 'missing-control', component: TestStep },
    ]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Should only have the valid step
    expect(wrapper.vm.availableSteps.length).toBe(1)
    expect(wrapper.vm.availableSteps[0].name).toBe('Valid Step')
  })

  it('should handle errors during content control check', async () => {
    // Configure mock to throw an error
    hasContentControlMock.mockImplementation(() => {
      throw new Error('Content control check failed')
    })

    const steps = [{ name: 'Test Step', contentControl: 'test-control', component: TestStep }]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Should set user-friendly fatal error message
    expect(wrapper.vm.fatalError).toContain('Unable to connect to Word document')

    // Should have no available steps
    expect(wrapper.vm.availableSteps.length).toBe(0)

    // Should log the error
    expect(loggerErrorMock).toHaveBeenCalled()
  })

  it('should use fallback component when step component is missing', async () => {
    // Mock the component to be null but still have a step
    const steps = [{ name: 'Missing Component', component: null }]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: {
        stubs: { PanelToggle: true },
      },
    })

    await flushPromises()

    // Should set user-friendly step error message about the missing component
    expect(wrapper.vm.stepError).toContain('is temporarily unavailable')

    // Instead of checking for the component in HTML (which might be stubbed),
    // verify that getCurrentComponent returns a component with the name 'FallbackStep'
    const currentComponent = wrapper.vm.getCurrentComponent()
    expect(currentComponent.name).toBe('FallbackStep')
  })

  it('should handle the case when there are no available steps', async () => {
    // Start with an empty steps array
    const steps = []

    const wrapper = mount(StepManager, {
      props: { steps },
      global: {
        stubs: { PanelToggle: true },
      },
    })

    await flushPromises()

    // Should set the appropriate user-friendly fatal error message
    expect(wrapper.vm.fatalError).toContain("doesn't contain the required elements")

    // Should log the error
    expect(loggerErrorMock).toHaveBeenCalledWith('No available steps found')

    // Verify that getCurrentComponent returns the FallbackStep
    const currentComponent = wrapper.vm.getCurrentComponent()
    expect(currentComponent.name).toBe('FallbackStep')

    // Check that the user-friendly fatal error message is set correctly
    expect(wrapper.vm.fatalError).toContain("doesn't contain the required elements")

    // In the test environment, the error div might not be rendered as expected
    // Focus on testing the component's internal state instead
  })

  it('should handle the case when all steps are filtered out', async () => {
    // Configure mock to filter out all content controls
    hasContentControlMock.mockResolvedValue(false)

    const steps = [
      { name: 'Step 1', contentControl: 'control-1', component: TestStep },
      { name: 'Step 2', contentControl: 'control-2', component: TestStep },
    ]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: {
        stubs: { PanelToggle: true },
      },
    })

    await flushPromises()

    // Should have no available steps
    expect(wrapper.vm.availableSteps.length).toBe(0)

    // Should set the appropriate user-friendly fatal error message
    expect(wrapper.vm.fatalError).toContain("doesn't contain the required elements")

    // Should log the error
    expect(loggerErrorMock).toHaveBeenCalledWith('No available steps found')

    // Verify that getCurrentComponent returns the FallbackStep
    const currentComponent = wrapper.vm.getCurrentComponent()
    expect(currentComponent.name).toBe('FallbackStep')
  })

  it('should clear step errors when navigating to another step', async () => {
    const steps = [
      { name: 'Step 1', component: TestStep },
      { name: 'Step 2', component: TestStep },
    ]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Manually set a step error
    wrapper.vm.stepError = 'Test step error'
    expect(wrapper.vm.stepError).toBe('Test step error')

    // Navigate to another step
    wrapper.vm.navigateToStep(1)

    // Step error should be cleared
    expect(wrapper.vm.stepError).toBeNull()
  })

  it('should allow manual dismissal of step errors', async () => {
    const steps = [{ name: 'Step 1', component: TestStep }]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Manually set a step error
    wrapper.vm.stepError = 'Test step error'
    expect(wrapper.vm.stepError).toBe('Test step error')

    // Clear the error manually
    wrapper.vm.clearStepError()

    // Step error should be cleared
    expect(wrapper.vm.stepError).toBeNull()
  })

  it('should clear step errors when completing a step (goToNextStep)', async () => {
    const steps = [
      { name: 'Step 1', component: TestStep },
      { name: 'Step 2', component: TestStep },
    ]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Start on step 0
    expect(wrapper.vm.currentStepIndex).toBe(0)

    // Set a step error
    wrapper.vm.stepError = 'Test step error'
    expect(wrapper.vm.stepError).toBe('Test step error')

    // Simulate step completion (this calls goToNextStep internally)
    wrapper.vm.handleStepComplete()

    // Should move to next step AND clear the error
    expect(wrapper.vm.currentStepIndex).toBe(1)
    expect(wrapper.vm.stepError).toBeNull()
  })

  it('should show user-friendly error messages', async () => {
    const steps = [{ name: 'Test Step', component: null }]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Should show user-friendly message, not technical details
    expect(wrapper.vm.stepError).toContain('temporarily unavailable')
    expect(wrapper.vm.stepError).toContain('Test Step')
    expect(wrapper.vm.stepError).not.toContain('Component missing')
  })

  it('should render accessible error messages with proper ARIA attributes', async () => {
    const steps = [{ name: 'Test Step', component: null }]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Should have step error
    expect(wrapper.vm.stepError).toBeTruthy()

    // Find the error div
    const errorDiv = wrapper.find('[role="alert"]')
    expect(errorDiv.exists()).toBe(true)
    expect(errorDiv.attributes('aria-live')).toBe('polite')
    expect(errorDiv.attributes('aria-describedby')).toContain('error-0')

    // Find the dismiss button
    const dismissButton = wrapper.find('button[aria-label="Dismiss error message"]')
    expect(dismissButton.exists()).toBe(true)
    expect(dismissButton.attributes('type')).toBe('button')
    expect(dismissButton.attributes('title')).toBe('Dismiss error message')
  })

  it('should dismiss error when clicking the dismiss button', async () => {
    const steps = [{ name: 'Test Step', component: null }]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Should have step error
    expect(wrapper.vm.stepError).toBeTruthy()

    // Find and click the dismiss button
    const dismissButton = wrapper.find('button[aria-label="Dismiss error message"]')
    expect(dismissButton.exists()).toBe(true)

    await dismissButton.trigger('click')

    // Error should be cleared
    expect(wrapper.vm.stepError).toBeNull()
  })
})
