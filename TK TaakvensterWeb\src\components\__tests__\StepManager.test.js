import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { h } from 'vue'
import StepManager from '../StepManager.vue'

// Create mock functions before mocking modules
const hasContentControlMock = vi.fn().mockResolvedValue(true)
const loggerErrorMock = vi.fn()

// Mock dependencies
vi.mock('../../composables/useOffice', () => ({
  useOffice: () => ({
    hasContentControl: hasContentControlMock,
  }),
}))

vi.mock('../../composables/useLogger', () => ({
  useLogger: () => ({
    debug: vi.fn(),
    info: vi.fn(),
    error: loggerErrorMock,
  }),
}))

// Mock FallbackStep component
vi.mock('../steps/FallbackStep.vue', () => ({
  default: {
    name: 'FallbackStep',
    render() {
      return h('div', { class: 'fallback-step' }, 'Fallback Step')
    },
  },
}))

// Mock PanelToggle component
vi.mock('../PanelToggle.vue', () => ({
  default: {
    name: 'PanelToggle',
    props: ['currentStep', 'steps'],
    render() {
      return h('div', { class: 'panel-toggle' }, 'Panel Toggle')
    },
  },
}))

describe('StepManager Error Handling', () => {
  // Define test components
  const TestStep = {
    name: 'TestStep',
    render() {
      return h('div', { class: 'test-step' }, 'Test Step')
    },
    emits: ['step-ready', 'step-complete', 'step-previous'],
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()

    // Reset mocks to default behavior
    hasContentControlMock.mockReset().mockResolvedValue(true)

    // Silence console errors during tests
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  it('should filter out steps with missing content controls', async () => {
    // Configure mock to filter out specific content control
    hasContentControlMock.mockImplementation(async (name) => {
      return name !== 'missing-control'
    })

    const steps = [
      { name: 'Valid Step', contentControl: 'valid-control', component: TestStep },
      { name: 'Missing Step', contentControl: 'missing-control', component: TestStep },
    ]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Should only have the valid step
    expect(wrapper.vm.availableSteps.length).toBe(1)
    expect(wrapper.vm.availableSteps[0].name).toBe('Valid Step')
  })

  it('should handle errors during content control check', async () => {
    // Configure mock to throw an error
    hasContentControlMock.mockImplementation(() => {
      throw new Error('Content control check failed')
    })

    const steps = [{ name: 'Test Step', contentControl: 'test-control', component: TestStep }]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: { stubs: { PanelToggle: true } },
    })

    await flushPromises()

    // Should set error message
    expect(wrapper.vm.errorMessage).toContain('Failed to check content control for step')

    // Should have no available steps
    expect(wrapper.vm.availableSteps.length).toBe(0)

    // Should log the error
    expect(loggerErrorMock).toHaveBeenCalled()
  })

  it('should use fallback component when step component is missing', async () => {
    // Mock the component to be null but still have a step
    const steps = [{ name: 'Missing Component', component: null }]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: {
        stubs: { PanelToggle: true },
      },
    })

    await flushPromises()

    // Should set error message about the missing component
    expect(wrapper.vm.errorMessage).toContain('Component missing for step')

    // Instead of checking for the component in HTML (which might be stubbed),
    // verify that getCurrentComponent returns a component with the name 'FallbackStep'
    const currentComponent = wrapper.vm.getCurrentComponent()
    expect(currentComponent.name).toBe('FallbackStep')
  })

  it('should handle the case when there are no available steps', async () => {
    // Start with an empty steps array
    const steps = []

    const wrapper = mount(StepManager, {
      props: { steps },
      global: {
        stubs: { PanelToggle: true },
      },
    })

    await flushPromises()

    // Should set the appropriate error message
    expect(wrapper.vm.errorMessage).toBe('No available steps found')

    // Should log the error
    expect(loggerErrorMock).toHaveBeenCalledWith('No available steps found')

    // Verify that getCurrentComponent returns the FallbackStep
    const currentComponent = wrapper.vm.getCurrentComponent()
    expect(currentComponent.name).toBe('FallbackStep')

    // Check that the error message is set correctly
    expect(wrapper.vm.errorMessage).toBe('No available steps found')

    // In the test environment, the error div might not be rendered as expected
    // Focus on testing the component's internal state instead
  })

  it('should handle the case when all steps are filtered out', async () => {
    // Configure mock to filter out all content controls
    hasContentControlMock.mockResolvedValue(false)

    const steps = [
      { name: 'Step 1', contentControl: 'control-1', component: TestStep },
      { name: 'Step 2', contentControl: 'control-2', component: TestStep },
    ]

    const wrapper = mount(StepManager, {
      props: { steps },
      global: {
        stubs: { PanelToggle: true },
      },
    })

    await flushPromises()

    // Should have no available steps
    expect(wrapper.vm.availableSteps.length).toBe(0)

    // Should set the appropriate error message
    expect(wrapper.vm.errorMessage).toBe('No available steps found')

    // Should log the error
    expect(loggerErrorMock).toHaveBeenCalledWith('No available steps found')

    // Verify that getCurrentComponent returns the FallbackStep
    const currentComponent = wrapper.vm.getCurrentComponent()
    expect(currentComponent.name).toBe('FallbackStep')
  })
})
