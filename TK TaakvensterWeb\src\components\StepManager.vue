<template>
  <div>
    <PanelToggle
      :current-step="currentStepIndex"
      :steps="availableSteps"
      @update:currentStep="navigateToStep"
    />

    <!-- Fatal errors block everything -->
    <div
      v-if="fatalError"
      class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md mt-2 text-center"
      role="alert"
      aria-live="assertive"
    >
      {{ fatalError }}
    </div>

    <!-- Step content with step-specific error handling -->
    <div v-else :key="currentStepIndex" class="mt-2 p-4">
      <!-- Step-specific errors show above the step content -->
      <div
        v-if="stepError"
        class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md mb-4 text-left relative"
        role="alert"
        aria-live="polite"
        :aria-describedby="`error-${currentStepIndex}`"
      >
        <span :id="`error-${currentStepIndex}`">{{ stepError }}</span>
        <button
          @click="clearStepError"
          class="absolute top-2 right-2 w-6 h-6 flex items-center justify-center rounded-full hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-colors duration-200"
          aria-label="Dismiss error message"
          type="button"
          title="Dismiss error message"
        >
          <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>

      <component
        :is="getCurrentComponent()"
        :error="stepError"
        @step-ready="handleStepReady"
        @step-complete="handleStepComplete"
        @step-previous="handleStepPrevious"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onErrorCaptured } from 'vue'
import { useOffice } from '../composables/useOffice'
import { useLogger } from '../composables/useLogger'
import PanelToggle from './PanelToggle.vue'
import FallbackStep from './steps/FallbackStep.vue'

const { debug, info, error } = useLogger()
const { hasContentControl } = useOffice()

// Check if we're in development mode
const isDevelopment = import.meta.env.DEV

// Error message configuration
const errorMessages = {
  stepError: isDevelopment
    ? 'This step encountered an issue (Development Mode). You can continue to other steps or check the console for details.'
    : 'This step encountered an issue. You can continue to other steps or dismiss this message.',
  componentMissing: (stepName) =>
    `Step '${stepName}' is temporarily unavailable. Please try other steps.`,
  contentControlError:
    'Unable to connect to Word document. Please ensure the document is open and try refreshing.',
  noStepsAvailable:
    "This document doesn't contain the required elements. Please ensure you're using a compatible Word template.",
  multipleErrors: (count) =>
    `Multiple connection issues occurred (${count} errors). Please refresh and try again.`,
}

const props = defineProps({
  steps: {
    type: Array,
    required: true,
    validator: (steps) =>
      steps.every(
        (step) =>
          typeof step.name === 'string' &&
          (step.contentControl === undefined || typeof step.contentControl === 'string') &&
          typeof step.component === 'object', // Component can be a Vue component or async import
      ),
  },
})

const availableSteps = ref([])
const currentStepIndex = ref(0)

// Error state management
const fatalError = ref(null) // Blocks all functionality
const stepError = ref(null) // Step-specific errors that allow navigation

// Handle errors from step components
onErrorCaptured((err, instance, info) => {
  console.error('Step component error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)

  // Set step-specific error (allows navigation to other steps)
  stepError.value = errorMessages.stepError

  // Log using existing logger
  error(`Step component error: ${err.message}`)

  // Stop error propagation - we handled it
  return false
})

onMounted(async () => {
  const errors = []
  const steps = await Promise.all(
    props.steps.map(async (step, index) => {
      try {
        if (!step.contentControl || (await hasContentControl(step.contentControl))) {
          return { ...step, id: index }
        }
        // Content control doesn't exist - this is not an error, just filtering
        return null
      } catch (err) {
        const errorMsg = `Failed to check content control for step "${step.name}": ${err.message}`
        errors.push(step.name) // Store step name for user-friendly message
        error(errorMsg) // Log technical details
        return null
      }
    }),
  )
  availableSteps.value = steps.filter((step) => step !== null)

  // Set appropriate error message based on what happened
  if (errors.length > 0) {
    // Show user-friendly fatal errors for content control issues
    fatalError.value =
      errors.length === 1
        ? errorMessages.contentControlError
        : errorMessages.multipleErrors(errors.length)
  } else if (availableSteps.value.length === 0) {
    // No errors, but no steps available (all content controls missing) - this is fatal
    fatalError.value = errorMessages.noStepsAvailable
    error('No available steps found')
  }
})

const currentStep = computed(() => availableSteps.value[currentStepIndex.value])

function getCurrentComponent() {
  // If no current step exists (e.g., no available steps), return fallback
  if (!currentStep.value) {
    return FallbackStep
  }

  const component = currentStep.value.component
  if (!component) {
    const technicalMsg = `Component missing for step "${currentStep.value.name}"`
    const userMsg = errorMessages.componentMissing(currentStep.value.name)

    // Set step-specific error (allows navigation to other steps)
    if (!stepError.value) {
      stepError.value = userMsg
    }
    error(technicalMsg) // Log technical details
    return FallbackStep
  }
  return component
}
function handleStepReady() {
  debug(`Step ${currentStep.value?.name} is ready`)
}

function handleStepComplete() {
  info(`Step ${currentStep.value?.name} completed`)
  goToNextStep()
}

function handleStepPrevious() {
  debug(`Step ${currentStep.value?.name} requested previous`)
  goToPreviousStep()
}

// Private helper function for all navigation
function setCurrentStep(newIndex) {
  stepError.value = null // Clear errors on any navigation
  currentStepIndex.value = newIndex
  debug(`Navigated to step ${newIndex + 1}`)
}

function goToNextStep() {
  if (currentStepIndex.value < availableSteps.value.length - 1) {
    setCurrentStep(currentStepIndex.value + 1)
  }
}

function goToPreviousStep() {
  if (currentStepIndex.value > 0) {
    setCurrentStep(currentStepIndex.value - 1)
  }
}

function navigateToStep(targetIndex) {
  if (targetIndex !== currentStepIndex.value) {
    setCurrentStep(targetIndex)
  }
}

// Error management functions
function clearStepError() {
  stepError.value = null
  debug('Step error cleared by user')
}
</script>
