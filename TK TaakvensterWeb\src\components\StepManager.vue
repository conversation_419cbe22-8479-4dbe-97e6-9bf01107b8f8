<template>
  <div>
    <PanelToggle
      :current-step="currentStepIndex"
      :steps="availableSteps"
      @update:currentStep="navigateToStep"
    />
    <div v-if="errorMessage" class="error">
      {{ errorMessage }}
    </div>
    <div v-else :key="currentStepIndex" class="step-container">
      <component
        :is="getCurrentComponent()"
        :error="errorMessage"
        @step-ready="handleStepReady"
        @step-complete="handleStepComplete"
        @step-previous="handleStepPrevious"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onErrorCaptured } from 'vue';
import { useOffice } from '../composables/useOffice';
import { useLogger } from '../composables/useLogger';
import PanelToggle from './PanelToggle.vue';
import FallbackStep from './steps/FallbackStep.vue';

const { debug, info, error } = useLogger();
const { hasContentControl } = useOffice();

const props = defineProps({
  steps: {
    type: Array,
    required: true,
    validator: (steps) =>
      steps.every(
        (step) =>
          typeof step.name === 'string' &&
          (step.contentControl === undefined || typeof step.contentControl === 'string') &&
          typeof step.component === 'object', // Component can be a Vue component or async import
      ),
  },
});

const availableSteps = ref([]);
const currentStepIndex = ref(0);
const errorMessage = ref(null);

// Handle errors from step components
onErrorCaptured((err, instance, info) => {
  console.error('Step component error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)

  // Set user-friendly error message
  errorMessage.value = 'Step failed to load. Please refresh the page to try again.'

  // Log using existing logger
  error(`Step component error: ${err.message}`)

  // Stop error propagation - we handled it
  return false
})

onMounted(async () => {
  const steps = await Promise.all(
    props.steps.map(async (step, index) => {
      try {
        if (!step.contentControl || await hasContentControl(step.contentControl)) {
          return { ...step, id: index };
        }
        return null;
      } catch (err) {
        errorMessage.value = `Failed to load step: ${step.name}`;
        error(`Failed to check content control for ${step.name}: ${err}`);
        return null;
      }
    }),
  );
  availableSteps.value = steps.filter((step) => step !== null);

  // Check if no steps are available after filtering, but only if no error message was already set
  if (availableSteps.value.length === 0 && !errorMessage.value) {
    errorMessage.value = 'No available steps found';
    error('No available steps found');
  }
});

const currentStep = computed(() => availableSteps.value[currentStepIndex.value]);

function getCurrentComponent() {
  // If no current step exists (e.g., no available steps), return fallback
  if (!currentStep.value) {
    return FallbackStep;
  }

  const component = currentStep.value.component;
  if (!component) {
    errorMessage.value = `Component for step ${currentStep.value.name} not found`;
    error(`Component for step ${currentStep.value.name} not found`);
    return FallbackStep;
  }
  return component;
}
function handleStepReady() {
  debug(`Step ${currentStep.value?.name} is ready`);
}

function handleStepComplete() {
  info(`Step ${currentStep.value?.name} completed`);
  goToNextStep();
}

function handleStepPrevious() {
  debug(`Step ${currentStep.value?.name} requested previous`);
  goToPreviousStep();
}

function goToNextStep() {
  if (currentStepIndex.value < availableSteps.value.length - 1) {
    currentStepIndex.value++;
  }
}

function goToPreviousStep() {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--;
  }
}

function navigateToStep(targetIndex) {
  if (targetIndex !== currentStepIndex.value) {
    currentStepIndex.value = targetIndex;
  }
}
</script>

<style scoped>
.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
  text-align: center;
}

.step-container {
  margin-top: 0.5rem;
  padding: 1rem;
}
</style>
