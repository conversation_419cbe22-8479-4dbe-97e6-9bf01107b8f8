<template>
  <div>
    <PanelToggle
      :current-step="currentStepIndex"
      :steps="availableSteps"
      @update:currentStep="navigateToStep"
    />

    <!-- Fatal errors block everything -->
    <div v-if="fatalError" class="error">
      {{ fatalError }}
    </div>

    <!-- Step content with step-specific error handling -->
    <div v-else :key="currentStepIndex" class="step-container">
      <!-- Step-specific errors show above the step content -->
      <div v-if="stepError" class="error step-error">
        {{ stepError }}
        <button @click="clearStepError" class="error-dismiss">✕ Dismiss</button>
      </div>

      <component
        :is="getCurrentComponent()"
        :error="stepError"
        @step-ready="handleStepReady"
        @step-complete="handleStepComplete"
        @step-previous="handleStepPrevious"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onErrorCaptured } from 'vue'
import { useOffice } from '../composables/useOffice'
import { useLogger } from '../composables/useLogger'
import PanelToggle from './PanelToggle.vue'
import FallbackStep from './steps/FallbackStep.vue'

const { debug, info, error } = useLogger()
const { hasContentControl } = useOffice()

// Check if we're in development mode
const isDevelopment = import.meta.env.DEV

// Error message configuration
const errorMessages = {
  stepError: isDevelopment
    ? 'This step encountered an issue (Development Mode). You can continue to other steps or check the console for details.'
    : 'This step encountered an issue. You can continue to other steps or dismiss this message.',
  componentMissing: (stepName) =>
    `Step '${stepName}' is temporarily unavailable. Please try other steps.`,
  contentControlError:
    'Unable to connect to Word document. Please ensure the document is open and try refreshing.',
  noStepsAvailable:
    "This document doesn't contain the required elements. Please ensure you're using a compatible Word template.",
  multipleErrors: (count) =>
    `Multiple connection issues occurred (${count} errors). Please refresh and try again.`,
}

const props = defineProps({
  steps: {
    type: Array,
    required: true,
    validator: (steps) =>
      steps.every(
        (step) =>
          typeof step.name === 'string' &&
          (step.contentControl === undefined || typeof step.contentControl === 'string') &&
          typeof step.component === 'object', // Component can be a Vue component or async import
      ),
  },
})

const availableSteps = ref([])
const currentStepIndex = ref(0)

// Error state management
const fatalError = ref(null) // Blocks all functionality
const stepError = ref(null) // Step-specific errors that allow navigation

// Handle errors from step components
onErrorCaptured((err, instance, info) => {
  console.error('Step component error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)

  // Set step-specific error (allows navigation to other steps)
  stepError.value = errorMessages.stepError

  // Log using existing logger
  error(`Step component error: ${err.message}`)

  // Stop error propagation - we handled it
  return false
})

onMounted(async () => {
  const errors = []
  const steps = await Promise.all(
    props.steps.map(async (step, index) => {
      try {
        if (!step.contentControl || (await hasContentControl(step.contentControl))) {
          return { ...step, id: index }
        }
        // Content control doesn't exist - this is not an error, just filtering
        return null
      } catch (err) {
        const errorMsg = `Failed to check content control for step "${step.name}": ${err.message}`
        errors.push(step.name) // Store step name for user-friendly message
        error(errorMsg) // Log technical details
        return null
      }
    }),
  )
  availableSteps.value = steps.filter((step) => step !== null)

  // Set appropriate error message based on what happened
  if (errors.length > 0) {
    // Show user-friendly fatal errors for content control issues
    fatalError.value =
      errors.length === 1
        ? errorMessages.contentControlError
        : errorMessages.multipleErrors(errors.length)
  } else if (availableSteps.value.length === 0) {
    // No errors, but no steps available (all content controls missing) - this is fatal
    fatalError.value = errorMessages.noStepsAvailable
    error('No available steps found')
  }
})

const currentStep = computed(() => availableSteps.value[currentStepIndex.value])

function getCurrentComponent() {
  // If no current step exists (e.g., no available steps), return fallback
  if (!currentStep.value) {
    return FallbackStep
  }

  const component = currentStep.value.component
  if (!component) {
    const technicalMsg = `Component missing for step "${currentStep.value.name}"`
    const userMsg = errorMessages.componentMissing(currentStep.value.name)

    // Set step-specific error (allows navigation to other steps)
    if (!stepError.value) {
      stepError.value = userMsg
    }
    error(technicalMsg) // Log technical details
    return FallbackStep
  }
  return component
}
function handleStepReady() {
  debug(`Step ${currentStep.value?.name} is ready`)
}

function handleStepComplete() {
  info(`Step ${currentStep.value?.name} completed`)
  goToNextStep()
}

function handleStepPrevious() {
  debug(`Step ${currentStep.value?.name} requested previous`)
  goToPreviousStep()
}

// Private helper function for all navigation
function setCurrentStep(newIndex) {
  stepError.value = null // Clear errors on any navigation
  currentStepIndex.value = newIndex
  debug(`Navigated to step ${newIndex + 1}`)
}

function goToNextStep() {
  if (currentStepIndex.value < availableSteps.value.length - 1) {
    setCurrentStep(currentStepIndex.value + 1)
  }
}

function goToPreviousStep() {
  if (currentStepIndex.value > 0) {
    setCurrentStep(currentStepIndex.value - 1)
  }
}

function navigateToStep(targetIndex) {
  if (targetIndex !== currentStepIndex.value) {
    setCurrentStep(targetIndex)
  }
}

// Error management functions
function clearStepError() {
  stepError.value = null
  debug('Step error cleared by user')
}
</script>

<style scoped>
.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
  text-align: center;
}

.step-error {
  position: relative;
  margin-bottom: 1rem;
  text-align: left;
}

.error-dismiss {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: #d32f2f;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.error-dismiss:hover {
  background-color: rgba(211, 47, 47, 0.1);
}

.step-container {
  margin-top: 0.5rem;
  padding: 1rem;
}
</style>
