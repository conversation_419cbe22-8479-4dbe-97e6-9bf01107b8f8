<template>
  <div>
    <PanelToggle
      :current-step="currentStepIndex"
      :steps="availableSteps"
      @update:currentStep="navigateToStep"
    />
    <div v-if="errorMessage" class="error">
      {{ errorMessage }}
    </div>
    <div v-else :key="currentStepIndex" class="step-container">
      <component
        :is="getCurrentComponent()"
        :error="errorMessage"
        @step-ready="handleStepReady"
        @step-complete="handleStepComplete"
        @step-previous="handleStepPrevious"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onErrorCaptured } from 'vue'
import { useOffice } from '../composables/useOffice'
import { useLogger } from '../composables/useLogger'
import PanelToggle from './PanelToggle.vue'
import FallbackStep from './steps/FallbackStep.vue'

const { debug, info, error } = useLogger()
const { hasContentControl } = useOffice()

const props = defineProps({
  steps: {
    type: Array,
    required: true,
    validator: (steps) =>
      steps.every(
        (step) =>
          typeof step.name === 'string' &&
          (step.contentControl === undefined || typeof step.contentControl === 'string') &&
          typeof step.component === 'object', // Component can be a Vue component or async import
      ),
  },
})

const availableSteps = ref([])
const currentStepIndex = ref(0)
const errorMessage = ref(null)

// Handle errors from step components
onErrorCaptured((err, instance, info) => {
  console.error('Step component error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)

  // Set user-friendly error message
  errorMessage.value = 'Step failed to load. Please refresh the page to try again.'

  // Log using existing logger
  error(`Step component error: ${err.message}`)

  // Stop error propagation - we handled it
  return false
})

onMounted(async () => {
  const errors = []
  const steps = await Promise.all(
    props.steps.map(async (step, index) => {
      try {
        if (!step.contentControl || (await hasContentControl(step.contentControl))) {
          return { ...step, id: index }
        }
        // Content control doesn't exist - this is not an error, just filtering
        return null
      } catch (err) {
        const errorMsg = `Failed to check content control for step "${step.name}": ${err.message}`
        errors.push(errorMsg)
        error(errorMsg)
        return null
      }
    }),
  )
  availableSteps.value = steps.filter((step) => step !== null)

  // Set appropriate error message based on what happened
  if (errors.length > 0) {
    // Show specific errors that occurred during content control checks
    errorMessage.value =
      errors.length === 1 ? errors[0] : `Multiple errors occurred: ${errors.join('; ')}`
  } else if (availableSteps.value.length === 0) {
    // No errors, but no steps available (all content controls missing)
    errorMessage.value = 'No available steps found'
    error('No available steps found')
  }
})

const currentStep = computed(() => availableSteps.value[currentStepIndex.value])

function getCurrentComponent() {
  // If no current step exists (e.g., no available steps), return fallback
  if (!currentStep.value) {
    return FallbackStep
  }

  const component = currentStep.value.component
  if (!component) {
    const errorMsg = `Component missing for step "${currentStep.value.name}"`
    // Only set error message if one isn't already set (preserve initialization errors)
    if (!errorMessage.value) {
      errorMessage.value = errorMsg
    }
    error(errorMsg)
    return FallbackStep
  }
  return component
}
function handleStepReady() {
  debug(`Step ${currentStep.value?.name} is ready`)
}

function handleStepComplete() {
  info(`Step ${currentStep.value?.name} completed`)
  goToNextStep()
}

function handleStepPrevious() {
  debug(`Step ${currentStep.value?.name} requested previous`)
  goToPreviousStep()
}

function goToNextStep() {
  if (currentStepIndex.value < availableSteps.value.length - 1) {
    currentStepIndex.value++
  }
}

function goToPreviousStep() {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
  }
}

function navigateToStep(targetIndex) {
  if (targetIndex !== currentStepIndex.value) {
    currentStepIndex.value = targetIndex
  }
}
</script>

<style scoped>
.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 16px;
  border-radius: 4px;
  margin-top: 8px;
  text-align: center;
}

.step-container {
  margin-top: 0.5rem;
  padding: 1rem;
}
</style>
