import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useErrorHandler } from '../composables/useErrorHandler';
import { createPinia, setActivePinia } from 'pinia';
import { useMessageBarStore } from '../stores/messageBarStore';

// Mock console methods
const consoleMock = {
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn()
};

describe('Error Handler Tests', () => {
  let messageStore;
  
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia());
    messageStore = useMessageBarStore();
    
    // Spy on messageStore methods
    vi.spyOn(messageStore, 'error');
    vi.spyOn(messageStore, 'warning');
    vi.spyOn(messageStore, 'info');
    
    // Mock console methods
    global.console = { ...console, ...consoleMock };
    Object.keys(consoleMock).forEach(key => consoleMock[key].mockClear());
  });
  
  describe('handleError', () => {
    it('should log error and show message by default', () => {
      const { handleError } = useErrorHandler();
      const testError = new Error('Test error');
      
      handleError(testError, 'test context');
      
      // Verify console logging
      expect(consoleMock.error).toHaveBeenCalledWith(
        expect.stringContaining('Error test context:'), 
        testError
      );
      
      // Verify message bar notification
      expect(messageStore.error).toHaveBeenCalledWith(
        expect.stringContaining('Test error')
      );
    });
    
    it('should respect silent option', () => {
      const { handleError } = useErrorHandler();
      const testError = new Error('Test error');
      
      handleError(testError, 'test context', { silent: true });
      
      // Should still log
      expect(consoleMock.error).toHaveBeenCalled();
      
      // But not show message
      expect(messageStore.error).not.toHaveBeenCalled();
    });
    
    it('should use custom log level', () => {
      const { handleError } = useErrorHandler();
      const testError = new Error('Test error');
      
      handleError(testError, 'test context', { logLevel: 'warn' });
      
      // Should use warn instead of error
      expect(consoleMock.warn).toHaveBeenCalled();
      expect(consoleMock.error).not.toHaveBeenCalled();
    });
    
    it('should use custom user message', () => {
      const { handleError } = useErrorHandler();
      const testError = new Error('Test error');
      const customMessage = 'Custom error message';
      
      handleError(testError, 'test context', { userMessage: customMessage });
      
      // Should use custom message
      expect(messageStore.error).toHaveBeenCalledWith(customMessage);
    });
  });
  
  describe('withErrorHandling', () => {
    it('should return result of successful operation', async () => {
      const { withErrorHandling } = useErrorHandler();
      const operation = vi.fn().mockResolvedValue('success');
      
      const result = await withErrorHandling(operation, 'test');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalled();
      expect(messageStore.error).not.toHaveBeenCalled();
    });
    
    it('should handle error and rethrow by default', async () => {
      const { withErrorHandling } = useErrorHandler();
      const testError = new Error('Operation failed');
      const operation = vi.fn().mockRejectedValue(testError);
      
      // Should rethrow
      await expect(withErrorHandling(operation, 'test')).rejects.toThrow(testError);
      
      // Should handle error
      expect(consoleMock.error).toHaveBeenCalled();
      expect(messageStore.error).toHaveBeenCalled();
    });
    
    it('should not rethrow when rethrow is false', async () => {
      const { withErrorHandling } = useErrorHandler();
      const testError = new Error('Operation failed');
      const operation = vi.fn().mockRejectedValue(testError);
      
      // Should not rethrow
      await expect(withErrorHandling(operation, 'test', { rethrow: false }))
        .resolves.toBeUndefined();
      
      // Should still handle error
      expect(consoleMock.error).toHaveBeenCalled();
      expect(messageStore.error).toHaveBeenCalled();
    });
  });
});
