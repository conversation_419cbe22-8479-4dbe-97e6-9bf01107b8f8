import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import CommissieStep from '../steps/CommissieStep.vue';
import { useMessageBarStore } from '../../stores/messageBarStore';

// Mock the message store
vi.mock('../../stores/messageBarStore', () => {
  const errorMock = vi.fn();
  return {
    useMessageBarStore: () => ({
      error: errorMock,
      success: vi.fn(),
      info: vi.fn(),
      warning: vi.fn(),
      clear: vi.fn()
    })
  };
});

// Create mock functions outside of the mock definitions
const insertTextInContentControlMock = vi.fn();
const withErrorHandlingMock = vi.fn();
const handleErrorMock = vi.fn();

// Mock the error handler
vi.mock('../../composables/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: handleErrorMock,
    withErrorHandling: withErrorHandlingMock
  })
}));

// Mock the Office API
vi.mock('../../composables/useOffice', () => ({
  useOffice: () => ({
    insertTextInContentControl: insertTextInContentControlMock
  })
}));

// Mock components
vi.mock('../steps/StepHeader.vue', () => ({
  default: {
    props: ['title', 'subTitle'],
    template: '<div>{{ title }} - {{ subTitle }}</div>'
  }
}));

vi.mock('../common/FlowbiteList.vue', () => ({
  default: {
    props: ['items', 'modelValue'],
    template: '<div class="flowbite-list"><slot name="item" v-bind="{ item: items[0] }"></slot></div>',
    emits: ['update:modelValue', 'item-click']
  }
}));

describe('Input Validation', () => {
  let messageStore;
  let wrapper;
  
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Reset the insertTextInContentControl mock to default behavior
    insertTextInContentControlMock.mockImplementation((tag, value) => {
      if (tag === 'fail') {
        return Promise.reject(new Error('Document insertion failed'));
      }
      return Promise.resolve(true);
    });
    
    // Setup withErrorHandling mock to directly call messageStore.error with the options.errorMessage
    withErrorHandlingMock.mockImplementation(async (operation, context, options) => {
      try {
        return await operation();
      } catch (error) {
        // Get the message store and call error directly
        const messageStore = useMessageBarStore();
        const message = options?.errorMessage || `Operation failed: ${error.message}`;
        messageStore.error(message);
        
        // Re-throw if needed
        if (options?.rethrow !== false) {
          throw error;
        }
      }
    });
    
    // Create a fresh pinia instance
    setActivePinia(createPinia());
    messageStore = useMessageBarStore();
    
    wrapper = mount(CommissieStep, {
      global: {
        plugins: [createPinia()]
      }
    });
  });
  
  it('should validate selection before processing', async () => {
    // Access the component's methods
    const { vm } = wrapper;
    
    // Setup withErrorHandling to call messageStore.error with the expected message
    withErrorHandlingMock.mockImplementation(async (operation, options) => {
      const messageStore = useMessageBarStore();
      messageStore.error('Selecteer eerst een commissie voordat u verder gaat.');
      throw new Error('Invalid selection'); // Simulate the error being thrown
    });
    
    // Call the method directly with an invalid index
    await vm.addSelectedCommissieToDocument(-1);
    await flushPromises();
    
    // Verify withErrorHandling was called
    expect(withErrorHandlingMock).toHaveBeenCalled();
    
    // Verify error was shown with the expected message
    expect(messageStore.error).toHaveBeenCalledWith(
      'Selecteer eerst een commissie voordat u verder gaat.'
    );
  });
  
  it('should handle errors during document insertion', async () => {
    // Override the mock for this test
    insertTextInContentControlMock.mockRejectedValue(new Error('Document insertion failed'));
    
    // Setup withErrorHandling to call messageStore.error with the expected message
    withErrorHandlingMock.mockImplementation(async (operation, options) => {
      const messageStore = useMessageBarStore();
      messageStore.error('Er is een fout opgetreden bij het toevoegen van de commissie \'Binnenlandse Zaken\' aan het document.');
      throw new Error('Document insertion failed'); // Simulate the error being thrown
    });
    
    // Simulate clicking on a commissie
    await wrapper.vm.handleCommissieClick({ index: 0 });
    await flushPromises();
    
    // Verify withErrorHandling was called
    expect(withErrorHandlingMock).toHaveBeenCalled();
    
    // Verify error was shown with the expected message
    expect(messageStore.error).toHaveBeenCalledWith(
      expect.stringContaining('Er is een fout opgetreden')
    );
  });
  
  it('should reset selection on error', async () => {
    // Override the mock for this test
    insertTextInContentControlMock.mockRejectedValue(new Error('Document insertion failed'));
    
    // Set a selection
    wrapper.vm.selectedCommissie = 0;
    
    // Simulate error during document insertion
    await wrapper.vm.addSelectedCommissieToDocument(0);
    
    // Verify selection was reset
    expect(wrapper.vm.selectedCommissie).toBeNull();
  });
});
