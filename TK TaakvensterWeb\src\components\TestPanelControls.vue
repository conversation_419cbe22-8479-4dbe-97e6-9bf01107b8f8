<template>
  <div>
    <!-- Toggle Button -->
    <div class="fixed bottom-4 right-4 z-40">
      <button 
        @click="togglePanel"
        class="bg-purple-500 text-white p-2 rounded-full shadow-lg hover:bg-purple-600"
        :title="showPanel ? 'Hide MessageBar Test Panel' : 'Show MessageBar Test Panel'"
      >
        <IconX v-if="showPanel" :size="20" stroke="currentColor" />
        <IconChartBar v-else :size="20" stroke="currentColor" />
      </button>
    </div>
    
    <!-- Test Panel Content -->
    <MessageBarTest v-if="showPanel" />
  </div>
</template>

<script setup>
import { IconChartBar, IconX } from '@tabler/icons-vue';
import MessageBarTest from './MessageBarTest.vue';

const props = defineProps({
  showPanel: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:showPanel']);

const togglePanel = () => {
  emit('update:showPanel', !props.showPanel);
};
</script>
