import { useMessageBarStore } from '@/stores/messageBarStore'

/**
 * Composable for consistent error handling across the application
 * Provides centralized error logging and user notification
 */
export function useErrorHandler() {
  const messageStore = useMessageBarStore()
  
  /**
   * Handle an error with logging and optional user notification
   * @param {Error} error - The error object
   * @param {string} context - Context description for debugging
   * @param {Object} options - Configuration options
   * @param {boolean} options.silent - Don't show user message (default: false)
   * @param {string} options.logLevel - Console log level (default: 'error')
   * @param {string} options.userMessage - Custom user message (default: auto-generated)
   */
  const handleError = (error, context = '', options = {}) => {
    const { 
      silent = false,
      logLevel = 'error',
      userMessage = null
    } = options
    
    // Always log for developers
    console[logLevel](`Error ${context}:`, error)
    
    // Show user message unless silent
    if (!silent) {
      const message = userMessage || `Operation failed: ${error.message}`
      messageStore.error(message)
    }
    
    // Future: Could add error reporting here
    // reportError(error, context)
  }
  
  /**
   * Wrap an async operation with error handling
   * @param {Function} operation - The async operation to execute
   * @param {string} context - Context description for debugging
   * @param {Object} options - Configuration options
   * @param {boolean} options.rethrow - Re-throw the error after handling (default: true)
   * @param {boolean} options.silent - Don't show user message (default: false)
   * @param {string} options.userMessage - Custom user message
   * @returns {Promise} - The result of the operation
   */
  const withErrorHandling = async (operation, context = '', options = {}) => {
    const { rethrow = true, ...handlerOptions } = options
    
    try {
      return await operation()
    } catch (error) {
      handleError(error, context, handlerOptions)
      
      // Re-throw if caller wants to handle it (default behavior)
      if (rethrow) {
        throw error
      }
    }
  }
  
  return { 
    handleError, 
    withErrorHandling 
  }
}
