# ⚠️ DEPRECATED: useMessageBar Composable

This composable has been **deprecated** in favor of the Pinia store approach.

## Migration Guide

### Before (Deprecated)
```javascript
import { useMessageBar } from '@/composables/useMessageBar';

const messageBar = useMessageBar();
messageBar.notify('Hello World', { intent: 'success' });
```

### After (Recommended)
```javascript
import { useMessageBarStore } from '@/stores/messageBarStore';

const messageStore = useMessageBarStore();
messageStore.notify('Hello World', { intent: 'success' });
```

## Why the Change?

1. **Better State Management**: Pinia provides centralized, reactive state management
2. **Consistency**: Standardizes on Vue 3 + Pinia architecture
3. **Maintainability**: Single source of truth for message management
4. **Performance**: Better optimization and devtools support

## Timeline

- **Current**: Composable marked as deprecated with console warnings
- **Next Version**: Composable will be moved to deprecated folder
- **Future Version**: Composable will be completely removed

## Need Help?

The Pinia store provides the exact same API, so migration should be straightforward. If you encounter any issues, please refer to the MessageBar component implementation for examples.
