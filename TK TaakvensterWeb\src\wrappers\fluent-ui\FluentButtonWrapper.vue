<template>
  <fluent-button 
    :appearance="appearance"
    :disabled="disabled"
    v-bind="$attrs"
  >
    <slot></slot>
  </fluent-button>
</template>

<script setup>
defineProps({
  appearance: {
    type: String,
    default: 'neutral',
    validator: (value) => ['neutral', 'accent', 'lightweight', 'outline', 'stealth'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  }
});
</script>

<style scoped>
/* No additional styling needed as we're using the native Fluent UI styling */
</style>
