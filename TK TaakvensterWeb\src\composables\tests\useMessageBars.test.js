import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useMessageBars } from '../fluent-ui/useMessageBars.js';
import { ref } from 'vue';

// Mock setTimeout for testing auto-dismissal
describe('useMessageBars', () => {
  let messageBars;

  beforeEach(() => {
    vi.useFakeTimers();
    messageBars = useMessageBars();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should generate unique IDs for message bars', () => {
    const id1 = messageBars.show('info', 'Test message 1');
    const id2 = messageBars.show('info', 'Test message 2');
    expect(id1).not.toBe(id2);
  });

  it('should add a message bar to activeMessageBars', () => {
    const id = messageBars.show('info', 'Test message');
    expect(messageBars.activeCount.value).toBe(1);
    expect(messageBars.activeMessageBars.get(id)).toBeDefined();
    expect(messageBars.activeMessageBars.get(id).message).toBe('Test message');
  });

  it('should dismiss a message bar', () => {
    const id = messageBars.show('info', 'Test message');
    messageBars.dismiss(id);
    expect(messageBars.activeCount.value).toBe(0);
    expect(messageBars.activeMessageBars.get(id)).toBeUndefined();
  });

  it('should auto-dismiss a message bar after duration', () => {
    const id = messageBars.show('info', 'Test message', { duration: 1000 });
    expect(messageBars.activeCount.value).toBe(1);
    vi.advanceTimersByTime(1000);
    expect(messageBars.activeCount.value).toBe(0);
    expect(messageBars.activeMessageBars.get(id)).toBeUndefined();
  });

  it('should limit the number of visible message bars', () => {
    messageBars.show('info', 'Message 1');
    messageBars.show('info', 'Message 2');
    messageBars.show('info', 'Message 3');
    messageBars.show('info', 'Message 4'); // This should remove the oldest one
    expect(messageBars.activeCount.value).toBe(3);
  });

  it('should handle message bar actions', () => {
    const actionHandler = vi.fn();
    const id = messageBars.show('info', 'Test message', {
      actions: [{ id: 'action1', text: 'Action', handler: actionHandler }]
    });
    
    // Access the internal handleMessageBarAction function through the Vue provide/inject system
    // Since we can't directly access it, we'll simulate what it does
    const messageBar = messageBars.activeMessageBars.get(id);
    const action = messageBar.actions.find(a => a.id === 'action1');
    action.handler();
    
    // Dismiss the message bar manually as the handleMessageBarAction would do
    messageBars.dismiss(id);
    
    expect(actionHandler).toHaveBeenCalled();
    // Verify the message was dismissed after action
    expect(messageBars.activeMessageBars.has(id)).toBe(false);
  });

  it('should clear all message bars', () => {
    messageBars.show('info', 'Message 1');
    messageBars.show('info', 'Message 2');
    messageBars.clear();
    expect(messageBars.activeCount.value).toBe(0);
  });
});
