<template>
  <div class="notification-test">
    <h2 class="text-lg font-semibold mb-4">Notification API Test</h2>
    
    <!-- Basic notification tests -->
    <div class="mb-6">
      <h3 class="text-md font-medium mb-2">Basic Notifications</h3>
      <div class="grid grid-cols-2 gap-2">
        <button 
          @click="testSuccess" 
          class="px-3 py-2 bg-green-100 text-green-700 rounded hover:bg-green-200 transition"
        >
          Success
        </button>
        <button 
          @click="testError" 
          class="px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition"
        >
          Error
        </button>
        <button 
          @click="testInfo" 
          class="px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition"
        >
          Info
        </button>
        <button 
          @click="testWarning" 
          class="px-3 py-2 bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 transition"
        >
          Warning
        </button>
      </div>
    </div>
    
    <!-- Force specific notification type -->
    <div class="mb-6">
      <h3 class="text-md font-medium mb-2">Force Specific Type</h3>
      <div class="grid grid-cols-2 gap-2">
        <button 
          @click="forceToast" 
          class="px-3 py-2 bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition"
        >
          Force Toast
        </button>
        <button 
          @click="forceMessageBar" 
          class="px-3 py-2 bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 transition"
        >
          Force MessageBar
        </button>
      </div>
    </div>
    
    <!-- Advanced options -->
    <div class="mb-6">
      <h3 class="text-md font-medium mb-2">Advanced Options</h3>
      <div class="grid grid-cols-2 gap-2">
        <button 
          @click="withActions" 
          class="px-3 py-2 bg-teal-100 text-teal-700 rounded hover:bg-teal-200 transition"
        >
          With Actions
        </button>
        <button 
          @click="customDuration" 
          class="px-3 py-2 bg-cyan-100 text-cyan-700 rounded hover:bg-cyan-200 transition"
        >
          Custom Duration
        </button>
      </div>
    </div>
    
    <!-- Controls -->
    <div class="mb-6">
      <h3 class="text-md font-medium mb-2">Controls</h3>
      <div class="grid grid-cols-3 gap-2">
        <button 
          @click="clearAll" 
          class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition"
        >
          Clear All
        </button>
        <button 
          @click="clearToasts" 
          class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition"
        >
          Clear Toasts
        </button>
        <button 
          @click="clearMessageBars" 
          class="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition"
        >
          Clear MessageBars
        </button>
      </div>
    </div>
    
    <!-- Stats -->
    <div class="text-sm text-gray-600">
      <div>Total notifications: {{ notifications.activeCount }}</div>
      <div>Toasts: {{ notifications.toastCount }}</div>
      <div>MessageBars: {{ notifications.messageBarCount }}</div>
    </div>
  </div>
</template>

<script setup>
import { useNotifications } from '../composables/useNotifications';

// Initialize the unified notification system
const notifications = useNotifications();

// Basic notification tests
const testSuccess = () => {
  notifications.success('Operation completed successfully!');
};

const testError = () => {
  notifications.error('An error occurred while processing your request.');
};

const testInfo = () => {
  notifications.info('New updates are available for your application.');
};

const testWarning = () => {
  notifications.warning('Your session will expire in 5 minutes.');
};

// Force specific notification type
const forceToast = () => {
  notifications.success('This is always shown as a toast.', { 
    useToast: true 
  });
};

const forceMessageBar = () => {
  notifications.info('This is always shown as a message bar.', { 
    useMessageBar: true 
  });
};

// Advanced options
const withActions = () => {
  notifications.info('Would you like to save your changes?', {
    useMessageBar: true,
    actions: [
      { 
        text: 'Save', 
        id: 'save', 
        handler: () => notifications.success('Changes saved!', { useToast: true })
      },
      { 
        text: 'Discard', 
        id: 'discard', 
        handler: () => notifications.info('Changes discarded.', { useToast: true })
      }
    ]
  });
};

const customDuration = () => {
  notifications.info('This notification will disappear in 10 seconds.', {
    duration: 10000
  });
};

// Controls
const clearAll = () => {
  notifications.clear();
};

const clearToasts = () => {
  notifications.clear('toast');
};

const clearMessageBars = () => {
  notifications.clear('messagebar');
};
</script>

<style scoped>
.notification-test {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 2rem auto;
}
</style>
