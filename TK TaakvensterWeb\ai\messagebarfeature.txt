MessageBar Feature Summary
Purpose
The MessageBar system provides critical top-of-page notifications in the TK Word Addin using Fluent UI Web Components.

Key Files
FluentMessageBarWrapper.vue
: Direct wrapper around the native Fluent UI <fluent-message-bar> web component
FluentMessageBar.vue
: Enhanced Vue component adding transitions, icons, actions, and auto-dismiss
MessageBarContainer.vue
: Container that renders active MessageBars with accessibility features
useMessageBars.js
: Core reactive composable for managing MessageBar state
notificationConfig.js
: Centralized configuration settings
Current State
The system uses a Vue 3 Composition API approach with reactive state management
MessageBars support different intents (success, error, info, warning)
Features include:
Auto-dismiss timers
Action buttons
FIFO queue for maximum visible MessageBars
Accessibility attributes
Transition animations
Architecture
useMessageBars.js
 provides the core reactive state and methods
Components inject this state via Vue's provide/inject
MessageBars are rendered in 
MessageBarContainer.vue
Each MessageBar uses 
FluentMessageBar.vue
 which wraps 
FluentMessageBarWrapper.vue
The wrapper directly maps to the Fluent UI web component API
API
success/error/info/warning: Convenience methods for showing MessageBars
show
: Main method to display a MessageBar with options
dismiss
: Remove a specific MessageBar
clear
: Remove all MessageBars
Would you like me to focus on any specific aspect of this system for more details?m