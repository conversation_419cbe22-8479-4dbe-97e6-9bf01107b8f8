import { ref, onMounted } from 'vue';

/**
 * Composable for checking Word API compatibility
 * 
 * @param {Object} options Configuration options
 * @param {string} options.requiredApiSet API set to check (default: 'WordApi')
 * @param {string} options.requiredVersion Minimum version required (default: '1.1')
 * @param {string} options.fallbackMessage Message to display when incompatible
 * @returns {Object} Compatibility state and message
 */
export function useWordCompatibility(options = {}) {
  const {
    requiredApiSet = 'WordApi',
    requiredVersion = '1.1',
    fallbackMessage = "This add-in requires Word 2016 or later. Some features may not be available in your version of Office."
  } = options;
  
  const isWordCompatible = ref(true);
  const errorMessage = ref(fallbackMessage);
  
  /**
   * Check if the Word API is supported
   */
  const checkCompatibility = () => {
    if (!window.Office?.context?.requirements) {
      isWordCompatible.value = false;
      return;
    }
    
    try {
      isWordCompatible.value = Office.context.requirements.isSetSupported(
        requiredApiSet, 
        requiredVersion
      );
      
      if (!isWordCompatible.value) {
        console.log("Word API not supported, using fallback mode");
      }
    } catch (error) {
      console.error('Error checking Word API support:', error);
      isWordCompatible.value = false;
    }
  };
  
  onMounted(() => {
    if (window.Office) {
      checkCompatibility();
    } else {
      Office.onReady(checkCompatibility);
    }
  });
  
  return {
    isWordCompatible,
    errorMessage,
    requiredApiSet,
    requiredVersion
  };
}
