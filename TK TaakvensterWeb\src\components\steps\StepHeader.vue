<template>
    <div class="container ms-fontWeight-regular">
        <p class="ms-font-xl ms-fontColor-neutralPrimary ms-fontWeight-semilight">
            {{ title }}
        </p>
        <p class="ms-font-m ms-fontColor-neutralSecondary">
        {{ subTitle }}
        </p>
    </div>
</template>
<script setup>
    const props = defineProps({
        title: {
            type: String,
            required: true
        },
        subTitle:{
            type: String,
            required: true
        }
    });
</script>
<style scoped>
.container {
    margin-bottom: 24px;
}
</style>