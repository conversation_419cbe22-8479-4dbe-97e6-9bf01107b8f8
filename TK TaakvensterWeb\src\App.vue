<template>
  <div id="app" class="ms-Fabric h-screen flex flex-col">

    <template v-if="!isWordCompatible">
      <MessageBarList />
      
      <StepManager :steps="stepConfig" />
      <Footer title="Tweede Kamer" />
      
      <TestPanelControls v-if="isDevelopment" v-model:show-panel="showMessageBarTest" />
    </template>
    <IncompatibleWordVersionPanel v-else />
  </div>
</template>

<script setup>
import { ref } from 'vue';

import Footer from "./components/Footer.vue";
import StepManager from './components/StepManager.vue';
import IncompatibleWordVersionPanel from './components/IncompatibleWordVersionPanel.vue';
import MessageBarList from './components/ui/MessageBar/MessageBarList.vue';
import TestPanelControls from './components/TestPanelControls.vue';

import { useWordCompatibility } from './composables/useWordCompatibility';
import { useStepConfig } from './composables/useStepConfig';
import { useEnvironment } from './composables/useEnvironment';

// Configure Word feature detection
const { isWordCompatible } = useWordCompatibility();

// Environment detection
const { isDevelopment } = useEnvironment();

// Development test panel flag
const showMessageBarTest = ref(false);

// Step configuration
const stepConfig = useStepConfig();
</script>