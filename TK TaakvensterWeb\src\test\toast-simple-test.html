<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simplified Toast System Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div id="app">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold mb-6 text-center">Simplified Toast Test</h1>
            
            <div class="space-y-4">
                <button @click="showSuccess" class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">
                    Success Toast
                </button>
                
                <button @click="showError" class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
                    Error Toast
                </button>
                
                <button @click="showInfo" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                    Info Toast
                </button>
                
                <button @click="showWarning" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded">
                    Warning Toast
                </button>
                
                <button @click="showPersistent" class="w-full bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded">
                    Persistent Toast
                </button>
                
                <button @click="clearAll" class="w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                    Clear All
                </button>
            </div>
            
            <div class="mt-6 text-sm text-gray-600">
                <p>Active Toasts: {{ activeCount }}</p>
                <p>System Ready: {{ isReady }}</p>
                <p>Can Show More: {{ canShowMore }}</p>
            </div>
        </div>
        
        <!-- Toast Container -->
        <div id="toast-container" 
             class="fixed top-4 right-4 z-50 space-y-2 pointer-events-none"
             role="region"
             aria-label="Notifications"
             aria-live="polite">
        </div>
    </div>

    <script type="module">
        // Mock the simplified toast system for testing
        const { createApp, ref, reactive, computed } = Vue;
        
        // Simple template system
        const useToastTemplates = () => {
            const templates = {
                success: { icon: '✓', bgClass: 'bg-green-100 border-green-500 text-green-900', iconClass: 'text-green-500' },
                error: { icon: '✕', bgClass: 'bg-red-100 border-red-500 text-red-900', iconClass: 'text-red-500' },
                info: { icon: 'ℹ', bgClass: 'bg-blue-100 border-blue-500 text-blue-900', iconClass: 'text-blue-500' },
                warning: { icon: '⚠', bgClass: 'bg-yellow-100 border-yellow-500 text-yellow-900', iconClass: 'text-yellow-500' }
            };
            
            const isLoaded = ref(true);
            
            const compileTemplate = (type, message, toastId) => {
                const template = templates[type];
                if (!template) return null;
                
                const escapeHtml = (text) => {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                };
                
                return `
                    <div class="flex items-center p-4 mb-4 text-sm border-l-4 ${template.bgClass} rounded-lg shadow-md" role="alert">
                        <span class="flex-shrink-0 inline-flex justify-center items-center w-8 h-8 rounded-full ${template.iconClass}">
                            ${template.icon}
                        </span>
                        <div class="ml-3 text-sm font-medium">
                            ${escapeHtml(message)}
                        </div>
                        <button type="button" 
                                class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 text-gray-500 hover:text-gray-900"
                                data-toast-close="${escapeHtml(toastId)}"
                                aria-label="Close">
                            <span class="sr-only">Close</span>
                            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                            </svg>
                        </button>
                    </div>
                `.trim();
            };
            
            return { isLoaded, compileTemplate, hasTemplate: (type) => templates.hasOwnProperty(type) };
        };
        
        // Simplified toast manager
        const useToastManager = (options = {}) => {
            const config = reactive({
                maxToasts: 3,
                defaultDuration: 5000,
                gravity: "top",
                position: "right",
                ...options
            });
            
            const activeToasts = reactive(new Map());
            const toastCounter = ref(0);
            const { compileTemplate, isLoaded: templatesLoaded, hasTemplate } = useToastTemplates();
            
            const activeCount = computed(() => activeToasts.size);
            const isReady = computed(() => templatesLoaded.value);
            const canShowMore = computed(() => activeCount.value < config.maxToasts);
            
            const generateToastId = () => {
                toastCounter.value++;
                return `toast-${Date.now()}-${toastCounter.value}`;
            };
            
            const enforceMaxLimit = () => {
                while (activeToasts.size >= config.maxToasts) {
                    let oldestToast = null;
                    let oldestTime = Infinity;
                    
                    activeToasts.forEach((toast, toastId) => {
                        if (toast.createdAt < oldestTime) {
                            oldestTime = toast.createdAt;
                            oldestToast = { id: toastId, ...toast };
                        }
                    });
                    
                    if (oldestToast) {
                        removeToast(oldestToast.id);
                    }
                }
            };
            
            const showToast = (type, message, options = {}) => {
                if (!isReady.value || !hasTemplate(type) || !message) {
                    return null;
                }
                
                try {
                    enforceMaxLimit();
                    
                    const toastId = generateToastId();
                    const mergedOptions = { ...config, ...options };
                    const persistent = mergedOptions.persistent || false;
                    const duration = mergedOptions.duration !== undefined ? mergedOptions.duration : config.defaultDuration;
                    
                    const toastHTML = compileTemplate(type, message, toastId);
                    if (!toastHTML) return null;
                    
                    const toastifyInstance = Toastify({
                        text: toastHTML,
                        duration: persistent ? -1 : duration,
                        gravity: mergedOptions.gravity || config.gravity,
                        position: mergedOptions.position || config.position,
                        stopOnFocus: true,
                        escapeMarkup: false,
                        className: "toastify-custom",
                        destination: "#toast-container",
                        style: {
                            background: "transparent",
                            boxShadow: "none",
                            padding: "0",
                            margin: "0 0 8px 0"
                        },
                        callback: () => {
                            activeToasts.delete(toastId);
                        }
                    });
                    
                    toastifyInstance.showToast();
                    
                    activeToasts.set(toastId, {
                        id: toastId,
                        type,
                        message,
                        persistent,
                        duration,
                        createdAt: Date.now(),
                        toastifyInstance
                    });
                    
                    setTimeout(() => {
                        const closeButton = document.querySelector(`[data-toast-close="${toastId}"]`);
                        if (closeButton) {
                            closeButton.addEventListener('click', () => removeToast(toastId));
                        }
                    }, 100);
                    
                    return toastId;
                } catch (error) {
                    console.error('Error showing toast:', error);
                    return null;
                }
            };
            
            const removeToast = (toastId) => {
                const toast = activeToasts.get(toastId);
                if (!toast) return;
                
                try {
                    if (toast.toastifyInstance) {
                        toast.toastifyInstance.removeElement(toast.toastifyInstance.toastElement);
                    }
                    activeToasts.delete(toastId);
                } catch (error) {
                    console.error(`Error removing toast ${toastId}:`, error);
                }
            };
            
            const clearAllToasts = () => {
                const toastIds = Array.from(activeToasts.keys());
                toastIds.forEach(toastId => removeToast(toastId));
            };
            
            return {
                activeToasts: Vue.readonly(activeToasts),
                activeCount,
                isReady,
                canShowMore,
                showToast,
                removeToast,
                clearAllToasts
            };
        };
        
        // Simplified useToastify
        const useToastify = (options = {}) => {
            const toastManager = useToastManager(options);
            
            return {
                activeToasts: toastManager.activeToasts,
                isReady: toastManager.isReady,
                activeCount: toastManager.activeCount,
                canShowMore: toastManager.canShowMore,
                success: (message, options = {}) => toastManager.showToast('success', message, options),
                error: (message, options = {}) => toastManager.showToast('error', message, options),
                info: (message, options = {}) => toastManager.showToast('info', message, options),
                warning: (message, options = {}) => toastManager.showToast('warning', message, options),
                clear: toastManager.clearAllToasts
            };
        };
        
        // Vue app
        createApp({
            setup() {
                const toast = useToastify();
                
                return {
                    ...toast,
                    showSuccess: () => toast.success('Success! Operation completed successfully.'),
                    showError: () => toast.error('Error! Something went wrong.'),
                    showInfo: () => toast.info('Info: Here is some useful information.'),
                    showWarning: () => toast.warning('Warning: Please be careful.'),
                    showPersistent: () => toast.info('This toast will stay until closed.', { persistent: true }),
                    clearAll: () => toast.clear()
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
