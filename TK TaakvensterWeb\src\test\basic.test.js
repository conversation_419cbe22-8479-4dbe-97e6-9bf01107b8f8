import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useErrorHandler } from '../composables/useErrorHandler';
import { createPinia, setActivePinia } from 'pinia';

// Mock the messageBarStore
vi.mock('../stores/messageBarStore', () => ({
  useMessageBarStore: vi.fn(() => ({
    notify: vi.fn(),
    dismiss: vi.fn(),
    clear: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
    handleAction: vi.fn()
  }))
}));

describe('Basic Error Handler Test', () => {
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia());
  });
  
  it('should handle errors correctly', () => {
    const { handleError } = useErrorHandler();
    const error = new Error('Test error');
    
    // This should not throw an error
    handleError(error, 'test context');
    
    expect(true).toBe(true);
  });
});
