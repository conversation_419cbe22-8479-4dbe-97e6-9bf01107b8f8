import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import FluentUI from './plugins/fluentUI'
import { useTheme } from './composables/useTheme'

// Create the Vue app
const app = createApp(App)

// Global error handler for Vue component errors
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue Error:', err)
  console.error('Component:', instance)
  console.error('Error Info:', info)
  
  // In production, you might want to send to error reporting service
  // if (import.meta.env.PROD) {
  //   // Send to Sentry, LogRocket, etc.
  // }
}

// Global handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason)
  console.error('Promise:', event.promise)
  
  // Prevent the default browser behavior (console error spam)
  event.preventDefault()
  
  // In production, you might want to send to error reporting service
  // if (import.meta.env.PROD) {
  //   // Send to error reporting service
  // }
})

// Use Pinia for state management
app.use(createPinia())

// Register Fluent UI Web Components (Beta version)
app.use(FluentUI)

// Apply Fluent UI theme
const { applyTheme } = useTheme()
applyTheme()

// Initialize Office.js when the app is mounted
if (typeof Office !== 'undefined') {
  Office.onReady(() => {
    app.mount('#app')
  })
} else {
  // For development outside of Office context
  app.mount('#app')
}
