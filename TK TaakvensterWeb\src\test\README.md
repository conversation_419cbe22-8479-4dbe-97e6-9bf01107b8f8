# TK Word Add-in Error Handling Tests

This directory contains unit tests for the error handling implementation in the TK Word Add-in application.

## Test Structure

The tests are organized as follows:

- `unit/composables/useErrorHandler.spec.js`: Tests for the error handling composable
- `unit/components/StepManager.spec.js`: Tests for the error boundary in StepManager
- `unit/components/RubriceringStep.spec.js`: Tests for input validation in step components

## Running Tests

You can run the tests using the following npm scripts:

### Run all tests once

```bash
npm test
```

### Run tests in watch mode (automatically re-run when files change)

```bash
npm run test:watch
```

### Run tests with coverage report

```bash
npm run test:coverage
```

## What's Being Tested

### useErrorHandler Composable

- `handleError`: Tests that errors are properly logged and displayed to the user
- `withErrorHandling`: Tests that async operations are properly wrapped with error handling

### StepManager Error Boundary

- Tests that errors in child components are captured and don't crash the entire app
- Tests that appropriate error messages are displayed to the user

### Step Components

- Tests that input validation is performed before making Office API calls
- Tests that Office API errors are properly handled and displayed to the user
- Tests that component state is reset on errors

## Adding More Tests

To add more tests:

1. Create a new test file in the appropriate directory
2. Import the component or composable you want to test
3. Mock any dependencies as needed
4. Write your test cases

## Best Practices

- Mock external dependencies (Office API, Pinia stores, etc.)
- Test both success and error scenarios
- Verify that error messages are displayed to the user
- Verify that component state is properly reset after errors
